// 基础类型定义
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

// 账号相关类型
export interface Account {
  id: string;
  value?: string; // cookie值，对应后端的value字段
  cookie_value?: string; // 兼容字段
  keyword_count?: number;
  status?: boolean; // 兼容字段
  enabled?: boolean; // 对应后端的enabled字段
  default_reply?: boolean;
  ai_reply?: boolean;
  auto_confirm?: boolean; // 对应后端的auto_confirm字段
  auto_confirm_delivery?: boolean; // 兼容字段
  remark?: string;
  pause_duration?: number; // 对应后端的pause_duration字段
  pause_time?: number; // 兼容字段
  updated_at?: string;
}

// 关键词类型
export interface Keyword {
  id: string;
  cookie_id: string;
  keyword: string;
  reply: string;
  item_id?: string;
  type: 'text' | 'image';
  image_path?: string;
  created_at: string;
  updated_at: string;
}

// 商品类型
export interface Item {
  id: string;
  cookie_id: string;
  item_id: string;
  title: string;
  detail: string;
  price: string;
  multi_spec: boolean;
  multi_quantity_delivery: boolean;
  updated_at: string;
}

// 订单类型
export interface Order {
  id: string;
  order_id: string;
  item_id: string;
  buyer_id: string;
  spec_info: string;
  quantity: number;
  amount: string;
  status: 'processing' | 'processed' | 'completed' | 'unknown';
  cookie_id: string;
  created_at: string;
  updated_at: string;
}

// 卡券类型
export interface Card {
  id: string;
  name: string;
  type: 'api' | 'text' | 'data';
  spec_info: string;
  data_count: number;
  delay_time: number;
  status: boolean;
  api_url?: string;
  fixed_text?: string;
  batch_data?: string[];
  created_at: string;
  updated_at: string;
}

// 发货规则类型
export interface DeliveryRule {
  id: string;
  keyword: string;
  card_id: string;
  card_name: string;
  card_type: string;
  delivery_count: number;
  status: boolean;
  delivered_count: number;
  created_at: string;
  updated_at: string;
}

// 通知渠道类型
export interface NotificationChannel {
  id: string;
  name: string;
  type: 'qq' | 'dingtalk' | 'feishu' | 'bark' | 'email' | 'webhook' | 'wechat' | 'telegram';
  config: Record<string, any>;
  status: boolean;
  created_at: string;
  updated_at: string;
}

// 消息通知配置类型
export interface MessageNotification {
  id: string;
  cookie_id: string;
  channel_id: string;
  channel_name: string;
  status: boolean;
  created_at: string;
  updated_at: string;
}

// 仪表盘统计类型
export interface DashboardStats {
  totalAccounts: number;
  totalKeywords: number;
  activeAccounts: number;
  totalOrders: number;
  totalUsers?: number; // 项目使用人数
  systemVersion?: string; // 系统版本
  hasUpdate?: boolean; // 是否有更新
  remoteVersion?: string; // 远程版本
  accounts: Array<{
    id: string;
    keyword_count: number;
    status: boolean;
    updated_at: string;
  }>;
}

// 分页参数类型
export interface PaginationParams {
  page: number;
  pageSize: number;
  total?: number;
}

// 搜索参数类型
export interface SearchParams {
  keyword?: string;
  cookieId?: string;
  status?: string;
}

// 菜单项类型
export interface MenuItem {
  key: string;
  icon: React.ReactNode;
  label: string;
  path: string;
  children?: MenuItem[];
}

// 用户信息类型
export interface UserInfo {
  id: string;
  username: string;
  role: 'admin' | 'user';
  token: string;
}

// 系统设置类型
export interface SystemSettings {
  version: string;
  [key: string]: any;
}

// 用户类型（用于用户管理）
export interface User {
  id: string;
  username: string;
  cookie_count: number;
  card_count: number;
  created_at: string;
  last_login?: string;
}

// 用户统计类型
export interface UserStats {
  totalUsers: number;
  totalCookies: number;
  totalCards: number;
  systemStatus: 'running' | 'error';
}

// 日志级别类型
export type LogLevel = 'info' | 'warning' | 'error' | 'debug';

// 日志条目类型
export interface LogEntry {
  timestamp: string;
  level: LogLevel;
  source?: string;
  function?: string;
  line?: number;
  message: string;
}

// 日志响应类型
export interface LogResponse {
  logs: LogEntry[];
  fileName: string;
  displayLines: number;
}
