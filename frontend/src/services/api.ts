import axios from 'axios';
import type {
  ApiResponse,
  Account,
  Keyword,
  Item,
  Order,
  Card,
  DeliveryRule,
  NotificationChannel,
  MessageNotification,
  DashboardStats,
  PaginationParams,
  SearchParams
} from '../types';

// API基础路径 - 统一使用/api前缀
const apiBase = location.origin + '/api';

// 创建axios实例
const api = axios.create({
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('auth_token');
      window.location.href = '/login';
    }
    // 错误处理交给具体的页面组件处理
    return Promise.reject(error);
  }
);

// 通用请求方法
const request = async <T>(
  method: 'GET' | 'POST' | 'PUT' | 'DELETE',
  url: string,
  data?: any,
  params?: any
): Promise<ApiResponse<T>> => {
  try {
    const response = await api.request({
      method,
      url,
      data,
      params,
    });
    return response.data;
  } catch (error: any) {
    throw error.response?.data || error;
  }
};

// 账号管理API
export const accountApi = {
  // 获取账号列表
  getAccounts: () => request<Account[]>('GET', `${apiBase}/cookies`),

  // 添加账号
  addAccount: (data: { id: string; cookie_value: string }) =>
    request('POST', `${apiBase}/cookies`, data),

  // 更新账号
  updateAccount: (id: string, data: Partial<Account>) =>
    request('PUT', `${apiBase}/cookies/${id}`, data),

  // 删除账号
  deleteAccount: (id: string) =>
    request('DELETE', `${apiBase}/cookies/${id}`),
  
  // 获取默认回复
  getDefaultReplies: (cookieId: string) =>
    request('GET', `${apiBase}/default-replies/${cookieId}`),

  // 更新默认回复
  updateDefaultReplies: (cookieId: string, replies: string[]) =>
    request('PUT', `${apiBase}/default-replies/${cookieId}`, { replies }),

  // 扫码登录相关
  // 生成二维码
  generateQRCode: () =>
    request('POST', `${apiBase}/qr-login/generate`),

  // 检查二维码状态
  checkQRCodeStatus: (sessionId: string) =>
    request('GET', `${apiBase}/qr-login/check/${sessionId}`),

  // 刷新Cookie
  refreshCookies: () =>
    request('POST', `${apiBase}/qr-login/refresh-cookies`),

  // 获取冷却状态
  getCooldownStatus: (cookieId: string) =>
    request('GET', `${apiBase}/qr-login/cooldown-status/${cookieId}`),

  // 重置冷却时间
  resetCooldown: (cookieId: string) =>
    request('POST', `${apiBase}/qr-login/reset-cooldown/${cookieId}`),
};

// 关键词管理API
export const keywordApi = {
  // 获取关键词列表
  getKeywords: (cookieId: string) =>
    request<Keyword[]>('GET', `${apiBase}/keywords-with-item-id/${cookieId}`),

  // 添加关键词
  addKeyword: (data: Omit<Keyword, 'id' | 'created_at' | 'updated_at'>) =>
    request('POST', `${apiBase}/keywords-with-item-id/${data.cookie_id}`, data),

  // 更新关键词
  updateKeyword: (id: string, data: Partial<Keyword>) =>
    request('PUT', `${apiBase}/keywords/${id}`, data),

  // 删除关键词
  deleteKeyword: (cookieId: string, index: number) =>
    request('DELETE', `${apiBase}/keywords/${cookieId}/${index}`),

  // 批量删除关键词
  batchDeleteKeywords: (ids: string[]) =>
    request('DELETE', `${apiBase}/keywords/batch`, { ids }),

  // 导出关键词
  exportKeywords: (cookieId: string) =>
    request('GET', `${apiBase}/keywords-export/${cookieId}`),

  // 导入关键词
  importKeywords: (cookieId: string, data: any) =>
    request('POST', `${apiBase}/keywords-import/${cookieId}`, data),

  // 上传图片关键词
  uploadImageKeyword: (formData: FormData) =>
    api.post(`${apiBase}/keywords/${formData.get('cookie_id')}/image`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    }),
};

// 商品管理API
export const itemApi = {
  // 获取商品列表
  getItems: (params?: SearchParams & PaginationParams) =>
    request<{ items: Item[]; total: number }>('GET', `${apiBase}/items`, null, params),

  // 获取指定账号商品
  getItemsByAccount: (cookieId: string, page?: number) =>
    request('GET', `${apiBase}/items/${cookieId}`, null, { page }),

  // 获取所有页商品
  getAllItemsFromAccount: (cookieId: string) =>
    request('POST', `${apiBase}/items/get-all-from-account`, { cookie_id: cookieId }),

  // 更新商品
  updateItem: (cookieId: string, itemId: string, data: Partial<Item>) =>
    request('PUT', `${apiBase}/items/${encodeURIComponent(cookieId)}/${encodeURIComponent(itemId)}`, data),

  // 删除商品
  deleteItem: (cookieId: string, itemId: string) =>
    request('DELETE', `${apiBase}/items/${encodeURIComponent(cookieId)}/${encodeURIComponent(itemId)}`),

  // 批量删除商品
  batchDeleteItems: (items: Array<{cookie_id: string, item_id: string}>) =>
    request('DELETE', `${apiBase}/items/batch`, items),
};

// 订单管理API
export const orderApi = {
  // 获取订单列表
  getOrders: (params?: SearchParams & PaginationParams) =>
    request<{ orders: Order[]; total: number }>('GET', `${apiBase}/admin/data/orders`, null, params),

  // 更新订单状态
  updateOrderStatus: (id: string, status: Order['status']) =>
    request('PUT', `${apiBase}/orders/${id}/status`, { status }),

  // 删除订单
  deleteOrder: (id: string) =>
    request('POST', `${apiBase}/admin/data/orders/delete`, { order_id: id }),

  // 批量删除订单
  batchDeleteOrders: (ids: string[]) =>
    request('DELETE', `${apiBase}/orders/batch`, { ids }),
};

// 仪表盘API
export const dashboardApi = {
  // 获取账号详情列表
  getAccountsDetails: () =>
    request<Account[]>('GET', `${apiBase}/cookies/details`),

  // 获取指定账号的关键词
  getAccountKeywords: (accountId: string) =>
    request<Keyword[]>('GET', `${apiBase}/keywords/${accountId}`),

  // 获取订单数据
  getOrders: () =>
    request<{ data: Order[] }>('GET', `${apiBase}/orders`),

  // 获取系统版本
  getSystemVersion: async () => {
    try {
      // 添加时间戳参数破坏缓存
      const timestamp = new Date().getTime();
      const response = await fetch(`/static/version.txt?t=${timestamp}`, {
        cache: 'no-cache', // 禁用缓存
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      });
      if (response.ok) {
        const version = await response.text();
        return { success: true, data: { version: version.trim() } };
      }
      return { success: false, error: '获取版本失败' };
    } catch (error) {
      return { success: false, error: '网络错误' };
    }
  },

  // 获取项目使用人数统计
  getProjectStats: async () => {
    try {
      const response = await fetch('http://xianyu.zhinianblog.cn/?action=stats');
      const result = await response.json();
      return { success: !result.error, data: result };
    } catch (error) {
      return { success: false, error: '网络错误' };
    }
  },

  // 检查版本更新
  checkVersionUpdate: async (currentVersion: string) => {
    try {
      const response = await fetch('http://xianyu.zhinianblog.cn/index.php?action=getVersion');
      const result = await response.json();
      return {
        success: !result.error,
        data: {
          remoteVersion: result.data,
          hasUpdate: result.data !== currentVersion
        }
      };
    } catch (error) {
      return { success: false, error: '网络错误' };
    }
  },

  // 获取更新详细信息
  getUpdateInfo: async () => {
    try {
      const response = await fetch('http://xianyu.zhinianblog.cn/index.php?action=getUpdateInfo');
      const result = await response.json();
      return { success: !result.error, data: result.data };
    } catch (error) {
      return { success: false, error: '网络错误' };
    }
  },
};

// 卡券管理API
export const cardApi = {
  // 获取卡券列表
  getCards: () => request<Card[]>('GET', `${apiBase}/cards`),

  // 添加卡券
  addCard: (data: Omit<Card, 'id' | 'created_at' | 'updated_at'>) =>
    request('POST', `${apiBase}/cards`, data),

  // 更新卡券
  updateCard: (id: string, data: Partial<Card>) =>
    request('PUT', `${apiBase}/cards/${id}`, data),

  // 删除卡券
  deleteCard: (id: string) =>
    request('DELETE', `${apiBase}/cards/${id}`),
};

// 发货规则API
export const deliveryApi = {
  // 获取发货规则列表
  getDeliveryRules: () => request<DeliveryRule[]>('GET', `${apiBase}/delivery-rules`),

  // 添加发货规则
  addDeliveryRule: (data: Omit<DeliveryRule, 'id' | 'created_at' | 'updated_at'>) =>
    request('POST', `${apiBase}/delivery-rules`, data),

  // 更新发货规则
  updateDeliveryRule: (id: string, data: Partial<DeliveryRule>) =>
    request('PUT', `${apiBase}/delivery-rules/${id}`, data),

  // 删除发货规则
  deleteDeliveryRule: (id: string) =>
    request('DELETE', `${apiBase}/delivery-rules/${id}`),
};

// 通知渠道API
export const notificationApi = {
  // 获取通知渠道列表
  getChannels: () => request<NotificationChannel[]>('GET', `${apiBase}/notification-channels`),

  // 添加通知渠道
  addChannel: (data: Omit<NotificationChannel, 'id' | 'created_at' | 'updated_at'>) =>
    request('POST', `${apiBase}/notification-channels`, data),

  // 更新通知渠道
  updateChannel: (id: string, data: Partial<NotificationChannel>) =>
    request('PUT', `${apiBase}/notification-channels/${id}`, data),

  // 删除通知渠道
  deleteChannel: (id: string) =>
    request('DELETE', `${apiBase}/notification-channels/${id}`),

  // 测试通知渠道
  testChannel: (id: string) =>
    request('POST', `${apiBase}/notification-channels/${id}/test`),

  // 获取消息通知配置
  getMessageNotifications: () => request<MessageNotification[]>('GET', `${apiBase}/message-notifications`),

  // 更新消息通知配置
  updateMessageNotification: (cookieId: string, channelId: string, enabled: boolean) =>
    request('PUT', `${apiBase}/message-notifications/${cookieId}/${channelId}`, { enabled }),
};

// 认证相关API
export const authApi = {
  // 登录
  login: (data: { username: string; password: string }) =>
    api.post(`${apiBase}/login`, data),

  // 登出
  logout: () =>
    api.post(`${apiBase}/logout`),

  // 验证token
  verify: () =>
    api.get(`${apiBase}/verify`),
};

// 用户管理API
export const userApi = {
  // 获取用户列表
  getUsers: () =>
    request('GET', `${apiBase}/admin/users`),

  // 获取用户统计（修正API端点）
  getUserStats: () =>
    request('GET', `${apiBase}/admin/stats`),

  // 删除用户
  deleteUser: (userId: string) =>
    request('DELETE', `${apiBase}/admin/users/${userId}`),
};

// 日志管理API
export const logApi = {
  // 获取系统日志
  getSystemLogs: (params: { lines?: number; level?: string }) =>
    request('GET', `${apiBase}/logs`, undefined, params),
};

// 系统设置API
export const systemApi = {
  // 获取系统版本
  getVersion: () =>
    request<{ version: string }>('GET', '/static/version.txt'),

  // 获取系统设置
  getSettings: () =>
    request('GET', `${apiBase}/system-settings`),

  // 更新系统设置
  updateSettings: (settings: any) =>
    request('PUT', `${apiBase}/system-settings`, settings),
};

// 数据管理API
export const dataApi = {
  // 获取数据库统计（修正API端点）
  getDatabaseStats: () =>
    request('GET', `${apiBase}/admin/stats`),

  // 获取备份文件列表（修正API端点）
  getBackupFiles: () =>
    request('GET', `${apiBase}/admin/backup/list`),

  // 创建备份（下载数据库备份）
  createBackup: async () => {
    try {
      const response = await api.get(`${apiBase}/admin/backup/download`, {
        responseType: 'blob',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      // 创建下载链接
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;

      // 获取文件名
      const contentDisposition = response.headers['content-disposition'];
      let filename = 'xianyu_backup.db';
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="(.+)"/);
        if (filenameMatch) {
          filename = filenameMatch[1];
        }
      }

      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      return { success: true, message: '备份下载成功' };
    } catch (error) {
      return { success: false, error: '备份下载失败' };
    }
  },

  // 下载备份
  downloadBackup: async (filename: string) => {
    const response = await api.get(`${apiBase}/admin/backup/download`, {
      responseType: 'blob'
    });
    return response;
  },

  // 删除备份
  deleteBackup: (filename: string) =>
    request('DELETE', `${apiBase}/admin/backups/${filename}`),

  // 恢复备份（上传数据库文件）
  restoreBackup: async (file: File) => {
    try {
      const formData = new FormData();
      formData.append('backup_file', file);

      const response = await api.post(`${apiBase}/admin/backup/upload`, formData, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'multipart/form-data'
        }
      });

      return { success: true, data: response.data };
    } catch (error: any) {
      return { success: false, error: error.response?.data?.detail || '恢复失败' };
    }
  },

  // 导出JSON备份
  exportBackup: async () => {
    try {
      const response = await api.get(`${apiBase}/backup/export`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: '导出备份失败' };
    }
  },

  // 导入JSON备份
  importBackup: async (file: File) => {
    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await api.post(`${apiBase}/backup/import`, formData, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'multipart/form-data'
        }
      });

      return { success: true, data: response.data };
    } catch (error: any) {
      return { success: false, error: error.response?.data?.detail || '导入失败' };
    }
  },
};

export default api;
