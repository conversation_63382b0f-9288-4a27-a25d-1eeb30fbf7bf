import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typography,
  Button,
  Space,
  Row,
  Col,
  Statistic,
  Alert,
  Progress,
  Modal,
  List,
  Tag,
  Popconfirm,
  App,
} from 'antd';
import {
  DatabaseOutlined,
  DownloadOutlined,
  UploadOutlined,
  DeleteOutlined,
  ReloadOutlined,
  <PERSON>DownloadOutlined,
  Exclamation<PERSON>ircleOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
} from '@ant-design/icons';
import { dataApi } from '../../services/api';

const { Title, Text } = Typography;

interface BackupFile {
  filename: string;
  size: string;
  created_at: string;
  type: 'auto' | 'manual';
}

interface DatabaseStats {
  users?: {
    total: number;
    active_today: number;
  };
  cookies?: {
    total: number;
    enabled: number;
  };
  cards?: {
    total: number;
    enabled: number;
  };
  system?: {
    uptime: string;
    version: string;
  };
}

const DataManagement: React.FC = () => {
  const { message } = App.useApp();
  const [loading, setLoading] = useState(false);
  const [backupLoading, setBackupLoading] = useState(false);
  const [restoreLoading, setRestoreLoading] = useState(false);
  const [backupFiles, setBackupFiles] = useState<BackupFile[]>([]);
  const [dbStats, setDbStats] = useState<DatabaseStats>({
    users: { total: 0, active_today: 0 },
    cookies: { total: 0, enabled: 0 },
    cards: { total: 0, enabled: 0 },
    system: { uptime: '未知', version: '1.0.0' },
  });
  const [backupProgress, setBackupProgress] = useState(0);
  const [showBackupModal, setShowBackupModal] = useState(false);

  // 加载数据库统计信息
  const loadDatabaseStats = async () => {
    try {
      const response = await dataApi.getDatabaseStats();
      if (response.success) {
        setDbStats(response.data);
      }
    } catch (error) {
      console.error('加载数据库统计失败:', error);
    }
  };

  // 加载备份文件列表
  const loadBackupFiles = async () => {
    try {
      setLoading(true);
      const response = await dataApi.getBackupFiles();
      if (response.success) {
        setBackupFiles(response.data || []);
      }
    } catch (error: any) {
      console.error('加载备份文件失败:', error);
      message.error('加载备份文件失败');
    } finally {
      setLoading(false);
    }
  };

  // 下载数据库备份
  const handleCreateBackup = async () => {
    try {
      setBackupLoading(true);
      setBackupProgress(0);
      setShowBackupModal(true);

      // 模拟备份进度
      const progressInterval = setInterval(() => {
        setBackupProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return prev;
          }
          return prev + 10;
        });
      }, 500);

      const response = await dataApi.createBackup();

      clearInterval(progressInterval);
      setBackupProgress(100);

      if (response.success) {
        message.success('数据库备份下载成功');
        setTimeout(() => {
          setShowBackupModal(false);
          setBackupProgress(0);
          loadDatabaseStats();
        }, 1000);
      } else {
        message.error(response.error || '下载备份失败');
        setShowBackupModal(false);
        setBackupProgress(0);
      }
    } catch (error: any) {
      message.error(error.message || '下载备份失败');
      setShowBackupModal(false);
      setBackupProgress(0);
    } finally {
      setBackupLoading(false);
    }
  };

  // 下载备份文件
  const handleDownloadBackup = async (filename: string) => {
    try {
      const response = await dataApi.downloadBackup(filename);

      // 创建下载链接
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      message.success('备份文件下载成功');
    } catch (error: any) {
      message.error('下载备份文件失败');
    }
  };

  // 删除备份文件
  const handleDeleteBackup = async (filename: string) => {
    try {
      const response = await dataApi.deleteBackup(filename);
      if (response.success) {
        message.success('备份文件删除成功');
        loadBackupFiles();
      }
    } catch (error: any) {
      message.error(error.message || '删除备份文件失败');
    }
  };

  // 恢复备份
  const handleRestoreBackup = async (filename: string) => {
    try {
      setRestoreLoading(true);
      const response = await dataApi.restoreBackup(filename);
      if (response.success) {
        message.success('数据恢复成功，请重新登录');
        // 恢复后需要重新登录
        setTimeout(() => {
          localStorage.clear();
          window.location.href = '/login';
        }, 2000);
      }
    } catch (error: any) {
      message.error(error.message || '数据恢复失败');
    } finally {
      setRestoreLoading(false);
    }
  };

  // 导出JSON备份
  const handleExportBackup = async () => {
    try {
      const response = await dataApi.exportBackup();
      if (response.success) {
        // 生成文件名
        const now = new Date();
        const timestamp = now.getFullYear() +
                        String(now.getMonth() + 1).padStart(2, '0') +
                        String(now.getDate()).padStart(2, '0') + '_' +
                        String(now.getHours()).padStart(2, '0') +
                        String(now.getMinutes()).padStart(2, '0') +
                        String(now.getSeconds()).padStart(2, '0');
        const filename = `xianyu_backup_${timestamp}.json`;

        // 创建下载链接
        const blob = new Blob([JSON.stringify(response.data, null, 2)], { type: 'application/json' });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        message.success('JSON备份导出成功');
      } else {
        message.error(response.error || '导出备份失败');
      }
    } catch (error: any) {
      message.error('导出备份失败');
    }
  };

  // 恢复数据库
  const handleRestoreDatabase = async (file: File) => {
    try {
      setRestoreLoading(true);
      const response = await dataApi.restoreBackup(file);
      if (response.success) {
        message.success('数据库恢复成功，请重新登录');
        setTimeout(() => {
          localStorage.clear();
          window.location.href = '/login';
        }, 2000);
      } else {
        message.error(response.error || '数据库恢复失败');
      }
    } catch (error: any) {
      message.error('数据库恢复失败');
    } finally {
      setRestoreLoading(false);
    }
  };

  // 恢复JSON备份
  const handleRestoreJSON = async (file: File) => {
    try {
      setRestoreLoading(true);
      const response = await dataApi.importBackup(file);
      if (response.success) {
        message.success('JSON备份恢复成功');
        loadDatabaseStats();
      } else {
        message.error(response.error || 'JSON备份恢复失败');
      }
    } catch (error: any) {
      message.error('JSON备份恢复失败');
    } finally {
      setRestoreLoading(false);
    }
  };

  // 刷新数据
  const handleRefresh = async () => {
    await loadDatabaseStats();
    message.success('数据已刷新');
  };

  useEffect(() => {
    loadDatabaseStats();
  }, []);

  return (
    <div className="data-management">
      {/* 页面头部 */}
      <div className="page-header">
        <div className="header-content">
          <Title level={2} style={{ margin: 0 }}>
            <DatabaseOutlined style={{ marginRight: 8 }} />
            数据管理
          </Title>
          <Text type="secondary">数据库备份、恢复和统计信息管理</Text>
        </div>
        <div className="header-extra">
          <Button
            icon={<ReloadOutlined />}
            onClick={handleRefresh}
            loading={loading}
          >
            刷新
          </Button>
        </div>
      </div>

      {/* 数据库统计 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="用户总数"
              value={dbStats.users?.total || 0}
              prefix={<DatabaseOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="今日活跃用户"
              value={dbStats.users?.active_today || 0}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="账号总数"
              value={dbStats.cookies?.total || 0}
              prefix={<DatabaseOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="启用账号数"
              value={dbStats.cookies?.enabled || 0}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 系统信息 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="卡券总数"
              value={dbStats.cards?.total || 0}
              prefix={<DatabaseOutlined />}
              valueStyle={{ color: '#eb2f96' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="启用卡券数"
              value={dbStats.cards?.enabled || 0}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#13c2c2' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="系统版本"
              value={dbStats.system?.version || '1.0.0'}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#52c41a', fontSize: '16px' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="运行时间"
              value={dbStats.system?.uptime || '未知'}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#1890ff', fontSize: '14px' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 备份操作 */}
      <Card
        title={
          <Space>
            <CloudDownloadOutlined />
            <span>数据备份</span>
          </Space>
        }
        style={{ marginBottom: 24 }}
      >
        <Alert
          message="备份说明"
          description="支持下载完整数据库备份文件和导出JSON格式备份。数据库备份包含完整的系统数据，JSON备份便于数据迁移。"
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />

        <Row gutter={16}>
          <Col xs={24} sm={12}>
            <Card size="small" title="数据库备份">
              <Space direction="vertical" style={{ width: '100%' }}>
                <Text type="secondary">下载完整的SQLite数据库文件</Text>
                <Button
                  type="primary"
                  icon={<DownloadOutlined />}
                  onClick={handleCreateBackup}
                  loading={backupLoading}
                  block
                >
                  下载数据库备份
                </Button>
              </Space>
            </Card>
          </Col>

          <Col xs={24} sm={12}>
            <Card size="small" title="JSON备份">
              <Space direction="vertical" style={{ width: '100%' }}>
                <Text type="secondary">导出JSON格式的数据备份</Text>
                <Button
                  icon={<CloudDownloadOutlined />}
                  onClick={handleExportBackup}
                  block
                >
                  导出JSON备份
                </Button>
              </Space>
            </Card>
          </Col>
        </Row>
      </Card>

      {/* 数据恢复 */}
      <Card
        title={
          <Space>
            <UploadOutlined />
            <span>数据恢复</span>
          </Space>
        }
      >
        <Alert
          message="恢复说明"
          description="支持上传数据库备份文件或JSON备份文件进行数据恢复。恢复操作将覆盖当前所有数据，请谨慎操作。"
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
        />

        <Row gutter={16}>
          <Col xs={24} sm={12}>
            <Card size="small" title="数据库恢复">
              <Space direction="vertical" style={{ width: '100%' }}>
                <Text type="secondary">上传SQLite数据库文件进行恢复</Text>
                <input
                  type="file"
                  accept=".db,.sqlite,.sqlite3"
                  onChange={(e) => {
                    const file = e.target.files?.[0];
                    if (file) {
                      handleRestoreDatabase(file);
                    }
                  }}
                  style={{ width: '100%' }}
                />
              </Space>
            </Card>
          </Col>

          <Col xs={24} sm={12}>
            <Card size="small" title="JSON恢复">
              <Space direction="vertical" style={{ width: '100%' }}>
                <Text type="secondary">上传JSON备份文件进行恢复</Text>
                <input
                  type="file"
                  accept=".json"
                  onChange={(e) => {
                    const file = e.target.files?.[0];
                    if (file) {
                      handleRestoreJSON(file);
                    }
                  }}
                  style={{ width: '100%' }}
                />
              </Space>
            </Card>
          </Col>
        </Row>
      </Card>

      {/* 备份进度模态框 */}
      <Modal
        title={
          <Space>
            <DownloadOutlined />
            <span>下载备份</span>
          </Space>
        }
        open={showBackupModal}
        footer={null}
        closable={false}
        centered
      >
        <div style={{ textAlign: 'center', padding: '20px 0' }}>
          <Progress
            type="circle"
            percent={backupProgress}
            status={backupProgress === 100 ? 'success' : 'active'}
            strokeColor={{
              '0%': '#108ee9',
              '100%': '#87d068',
            }}
          />
          <div style={{ marginTop: 16 }}>
            <Text>
              {backupProgress === 100 ? '备份下载完成' : '正在准备备份下载...'}
            </Text>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default DataManagement;
