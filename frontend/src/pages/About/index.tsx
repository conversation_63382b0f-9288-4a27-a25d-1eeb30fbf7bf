import React, { useState, useEffect } from 'react';
import {
  Card,
  Typography,
  Row,
  Col,
  Space,
  Divider,
  Tag,
  Button,
  Descriptions,
  Alert,
  Statistic
} from 'antd';
import {
  InfoCircleOutlined,
  GithubOutlined,
  BugOutlined,
  HeartOutlined,
  RocketOutlined,
  TeamOutlined,
  ClockCircleOutlined,
  DatabaseOutlined,
  ApiOutlined,
  SafetyOutlined
} from '@ant-design/icons';

const { Title, Text, Paragraph } = Typography;

const About: React.FC = () => {
  const [systemInfo, setSystemInfo] = useState({
    version: 'v1.0.0',
    buildTime: '2024-12-30 10:30:00',
    uptime: '运行中...',
    totalUsers: 0,
    totalMessages: 0,
    totalKeywords: 0
  });

  useEffect(() => {
    loadSystemInfo();
  }, []);

  const loadSystemInfo = async () => {
    try {
      // 这里应该调用API获取系统信息
      // const info = await systemApi.getSystemInfo();
      // setSystemInfo(info);
    } catch (error) {
      console.error('加载系统信息失败:', error);
    }
  };

  const features = [
    {
      icon: <ApiOutlined style={{ color: '#1890ff' }} />,
      title: '智能回复系统',
      description: '关键词匹配 + AI智能回复，支持指定商品回复和优先级策略'
    },
    {
      icon: <DatabaseOutlined style={{ color: '#52c41a' }} />,
      title: '自动发货功能',
      description: '智能匹配发货规则，支持多规格商品和延时发货'
    },
    {
      icon: <SafetyOutlined style={{ color: '#faad14' }} />,
      title: '多账号管理',
      description: '支持多个闲鱼账号同时运行，独立配置和监控'
    },
    {
      icon: <TeamOutlined style={{ color: '#722ed1' }} />,
      title: '多用户系统',
      description: '完整的用户注册登录，数据隔离和权限控制'
    }
  ];

  const techStack = [
    { name: 'React', version: '19.x', color: '#61dafb' },
    { name: 'TypeScript', version: '5.8.x', color: '#3178c6' },
    { name: 'Ant Design', version: '5.27.x', color: '#1890ff' },
    { name: 'Vite', version: '7.x', color: '#646cff' },
    { name: 'Python', version: '3.11+', color: '#3776ab' },
    { name: 'FastAPI', version: 'Latest', color: '#009688' },
    { name: 'SQLite', version: '3.x', color: '#003b57' },
    { name: 'WebSocket', version: 'Latest', color: '#010101' }
  ];

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}>
          <InfoCircleOutlined style={{ marginRight: '8px' }} />
          关于系统
        </Title>
        <Text type="secondary">🐟 闲鱼自动回复系统 - 智能化的闲鱼店铺管理工具</Text>
      </div>

      <Row gutter={[24, 24]}>
        {/* 系统信息 */}
        <Col xs={24} lg={12}>
          <Card
            title={
              <Space>
                <RocketOutlined />
                系统信息
              </Space>
            }
          >
            <Descriptions column={1} size="small">
              <Descriptions.Item label="系统版本">
                <Tag color="blue">{systemInfo.version}</Tag>
              </Descriptions.Item>
              <Descriptions.Item label="构建时间">
                <Text code>{systemInfo.buildTime}</Text>
              </Descriptions.Item>
              <Descriptions.Item label="运行时间">
                <Space>
                  <ClockCircleOutlined />
                  {systemInfo.uptime}
                </Space>
              </Descriptions.Item>
            </Descriptions>

            <Divider />

            <Row gutter={16}>
              <Col span={8}>
                <Statistic
                  title="用户数量"
                  value={systemInfo.totalUsers}
                  prefix={<TeamOutlined />}
                />
              </Col>
              <Col span={8}>
                <Statistic
                  title="消息总数"
                  value={systemInfo.totalMessages}
                  prefix={<ApiOutlined />}
                />
              </Col>
              <Col span={8}>
                <Statistic
                  title="关键词数"
                  value={systemInfo.totalKeywords}
                  prefix={<DatabaseOutlined />}
                />
              </Col>
            </Row>
          </Card>
        </Col>

        {/* 功能特性 */}
        <Col xs={24} lg={12}>
          <Card
            title={
              <Space>
                <HeartOutlined />
                功能特性
              </Space>
            }
          >
            <Space direction="vertical" style={{ width: '100%' }} size="middle">
              {features.map((feature, index) => (
                <div key={index} style={{ display: 'flex', alignItems: 'flex-start' }}>
                  <div style={{ marginRight: '12px', marginTop: '4px' }}>
                    {feature.icon}
                  </div>
                  <div style={{ flex: 1 }}>
                    <Text strong>{feature.title}</Text>
                    <br />
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      {feature.description}
                    </Text>
                  </div>
                </div>
              ))}
            </Space>
          </Card>
        </Col>

        {/* 技术栈 */}
        <Col xs={24}>
          <Card
            title={
              <Space>
                <GithubOutlined />
                技术栈
              </Space>
            }
          >
            <Space wrap>
              {techStack.map((tech, index) => (
                <Tag
                  key={index}
                  color={tech.color}
                  style={{
                    padding: '4px 12px',
                    fontSize: '14px',
                    borderRadius: '6px'
                  }}
                >
                  {tech.name} {tech.version}
                </Tag>
              ))}
            </Space>
          </Card>
        </Col>

        {/* 项目信息 */}
        <Col xs={24}>
          <Card
            title={
              <Space>
                <InfoCircleOutlined />
                项目信息
              </Space>
            }
          >
            <Row gutter={[24, 24]}>
              <Col xs={24} md={12}>
                <Paragraph>
                  <Title level={4}>项目简介</Title>
                  <Text>
                    一个功能完整的闲鱼自动回复和管理系统，采用现代化的技术架构，
                    支持多用户、多账号管理，具备智能回复、自动发货、商品管理等企业级功能。
                    基于Python异步编程，使用FastAPI提供RESTful API，SQLite数据库存储，支持Docker一键部署。
                  </Text>
                </Paragraph>

                <Paragraph>
                  <Title level={4}>核心功能</Title>
                  <ul>
                    <li>🤖 智能回复系统 - 关键词匹配 + AI智能回复</li>
                    <li>🚚 自动发货功能 - 智能匹配发货规则，支持多规格</li>
                    <li>📱 多账号管理 - 支持多个闲鱼账号同时运行</li>
                    <li>👥 多用户系统 - 完整的用户注册登录和权限管理</li>
                    <li>🛍️ 商品管理 - 自动收集商品信息，支持批量操作</li>
                    <li>🔍 商品搜索 - 基于Playwright获取真实闲鱼商品数据</li>
                    <li>📊 系统监控 - 实时日志和性能监控</li>
                    <li>🔐 安全特性 - 多层加密保护和数据隔离</li>
                  </ul>
                </Paragraph>
              </Col>

              <Col xs={24} md={12}>
                <Alert
                  message="开源项目"
                  description={
                    <div>
                      <Paragraph style={{ marginBottom: '12px' }}>
                        本项目采用开源协议，仅供学习研究使用，严禁商业用途！
                        欢迎贡献代码和提出建议。
                      </Paragraph>
                      <Space>
                        <Button
                          type="primary"
                          icon={<GithubOutlined />}
                          href="https://github.com/zhinianboke/xianyu-auto-reply"
                          target="_blank"
                        >
                          查看源码
                        </Button>
                        <Button
                          icon={<BugOutlined />}
                          href="https://github.com/zhinianboke/xianyu-auto-reply/issues"
                          target="_blank"
                        >
                          反馈问题
                        </Button>
                      </Space>
                    </div>
                  }
                  type="warning"
                  showIcon
                />
              </Col>
            </Row>
          </Card>
        </Col>

        {/* 项目统计 */}
        <Col xs={24} lg={12}>
          <Card
            title={
              <Space>
                <DatabaseOutlined />
                项目统计
              </Space>
            }
          >
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <Statistic
                  title="代码行数"
                  value={10000}
                  suffix="+"
                  prefix={<ApiOutlined />}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="功能模块"
                  value={15}
                  suffix="+"
                  prefix={<DatabaseOutlined />}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="API接口"
                  value={50}
                  suffix="+"
                  prefix={<ApiOutlined />}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="数据库表"
                  value={20}
                  suffix="+"
                  prefix={<DatabaseOutlined />}
                />
              </Col>
            </Row>
          </Card>
        </Col>

        {/* 技术特性 */}
        <Col xs={24} lg={12}>
          <Card
            title={
              <Space>
                <RocketOutlined />
                技术特性
              </Space>
            }
          >
            <Space direction="vertical" style={{ width: '100%' }} size="small">
              <div>
                <Tag color="blue">微服务架构</Tag>
                <Text type="secondary">模块化设计，易于维护和扩展</Text>
              </div>
              <div>
                <Tag color="green">异步编程</Tag>
                <Text type="secondary">基于asyncio的高性能异步处理</Text>
              </div>
              <div>
                <Tag color="orange">WebSocket长连接</Tag>
                <Text type="secondary">实时消息处理，低延迟响应</Text>
              </div>
              <div>
                <Tag color="purple">容器化部署</Tag>
                <Text type="secondary">Docker容器化，支持一键部署</Text>
              </div>
              <div>
                <Tag color="cyan">多层加密</Tag>
                <Text type="secondary">敏感代码采用多层编码混淆</Text>
              </div>
              <div>
                <Tag color="red">数据隔离</Tag>
                <Text type="secondary">每个用户的数据完全独立</Text>
              </div>
            </Space>
          </Card>
        </Col>

        {/* 版权声明 */}
        <Col xs={24}>
          <Card
            title={
              <Space>
                <SafetyOutlined />
                版权声明与使用条款
              </Space>
            }
          >
            <Alert
              message="重要声明"
              description={
                <div>
                  <Paragraph>
                    <Text strong>本项目仅供学习和研究使用，严禁商业用途！</Text>
                  </Paragraph>
                  <Paragraph>
                    <Text type="secondary">
                      项目作者：zhinianboke |
                      项目地址：https://github.com/zhinianboke/xianyu-auto-reply
                    </Text>
                  </Paragraph>
                  <Paragraph>
                    <Text type="secondary">
                      使用本项目即表示您已阅读、理解并同意遵守相关使用条款。
                      如需商业使用或特殊授权，请通过项目交流群联系作者进行协商。
                    </Text>
                  </Paragraph>
                </div>
              }
              type="error"
              showIcon
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default About;
