import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Table,
  Tag,
  Space,
  Typography,
  Badge,
  Spin,
  App,
} from 'antd';
import {
  UserOutlined,
  MessageOutlined,
  CheckCircleOutlined,
  FileTextOutlined,
  ReloadOutlined,
  TeamOutlined,
} from '@ant-design/icons';
import { dashboardApi } from '../../services/api';
import type { DashboardStats } from '../../types';
import dayjs from 'dayjs';

const { Title, Text } = Typography;

interface AccountDetail {
  id: string;
  keyword_count: number;
  status: boolean;
  updated_at: string;
}

const Dashboard: React.FC = () => {
  const { message } = App.useApp();
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState<DashboardStats>({
    totalAccounts: 0,
    totalKeywords: 0,
    activeAccounts: 0,
    totalOrders: 0,
    totalUsers: 0,
    systemVersion: '加载中...',
    hasUpdate: false,
    remoteVersion: '',
    accounts: [],
  });
  const [systemVersion, setSystemVersion] = useState('加载中...');
  const [updateAvailable, setUpdateAvailable] = useState(false);

  // 加载仪表盘数据
  const loadDashboardData = async () => {
    try {
      setLoading(true);

      // 1. 获取账号详情列表
      const accountsResponse = await dashboardApi.getAccountsDetails();

      if (!accountsResponse.success) {
        throw new Error('获取账号列表失败');
      }

      const accounts = accountsResponse.data || [];

      // 2. 为每个账号获取关键词信息
      const accountsWithKeywords = await Promise.all(
        accounts.map(async (account) => {
          try {
            const keywordsResponse = await dashboardApi.getAccountKeywords(account.id);
            const keywords = keywordsResponse.success ? keywordsResponse.data || [] : [];
            return {
              id: account.id,
              keyword_count: keywords.length,
              status: account.status !== false, // 默认启用
              updated_at: account.updated_at || new Date().toISOString(),
            };
          } catch (error) {
            console.error(`获取账号 ${account.id} 关键词失败:`, error);
            return {
              id: account.id,
              keyword_count: 0,
              status: account.status !== false,
              updated_at: account.updated_at || new Date().toISOString(),
            };
          }
        })
      );

      // 3. 计算统计数据
      const totalAccounts = accounts.length;
      const activeAccounts = accountsWithKeywords.filter(acc => acc.status).length;
      const totalKeywords = accountsWithKeywords.reduce((sum, acc) => sum + acc.keyword_count, 0);

      // 4. 获取订单数量
      let totalOrders = 0;
      try {
        const ordersResponse = await dashboardApi.getOrders();
        if (ordersResponse.success && ordersResponse.data?.data) {
          totalOrders = ordersResponse.data.data.length;
        }
      } catch (error) {
        console.error('获取订单数量失败:', error);
      }

      // 5. 获取系统版本
      let currentVersion = 'v1.0.0';
      try {
        const versionResponse = await dashboardApi.getSystemVersion();
        if (versionResponse.success) {
          currentVersion = versionResponse.data.version.trim();
          setSystemVersion(currentVersion);
        }
      } catch (error) {
        console.warn('获取系统版本失败:', error);
      }

      // 6. 获取项目使用人数
      let totalUsers = 0;
      try {
        const projectStatsResponse = await dashboardApi.getProjectStats();
        if (projectStatsResponse.success && !projectStatsResponse.data.error) {
          totalUsers = projectStatsResponse.data.total_users || 0;
        }
      } catch (error) {
        console.error('获取项目使用人数失败:', error);
      }

      // 7. 检查版本更新
      let hasUpdate = false;
      let remoteVersion = '';
      try {
        const updateResponse = await dashboardApi.checkVersionUpdate(currentVersion);
        if (updateResponse.success) {
          hasUpdate = updateResponse.data.hasUpdate;
          remoteVersion = updateResponse.data.remoteVersion;
          setUpdateAvailable(hasUpdate);
        }
      } catch (error) {
        console.error('检查版本更新失败:', error);
      }

      // 8. 更新状态
      setStats({
        totalAccounts,
        totalKeywords,
        activeAccounts,
        totalOrders,
        totalUsers,
        systemVersion: currentVersion,
        hasUpdate,
        remoteVersion,
        accounts: accountsWithKeywords,
      });

    } catch (error) {
      console.error('加载仪表盘数据失败:', error);
      message.error('加载仪表盘数据失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadDashboardData();
  }, []);

  // 账号详情表格列配置
  const columns = [
    {
      title: '账号ID',
      dataIndex: 'id',
      key: 'id',
      width: '25%',
      render: (text: string) => (
        <Text code copyable={{ text }}>
          {text}
        </Text>
      ),
    },
    {
      title: '关键词数量',
      dataIndex: 'keyword_count',
      key: 'keyword_count',
      width: '20%',
      render: (count: number) => (
        <Badge
          count={count}
          showZero
          color={count > 0 ? '#52c41a' : '#d9d9d9'}
          style={{ backgroundColor: count > 0 ? '#52c41a' : '#d9d9d9' }}
        />
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: '15%',
      render: (status: boolean) => (
        <Tag color={status ? 'success' : 'default'} icon={status ? <CheckCircleOutlined /> : undefined}>
          {status ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '最后更新',
      dataIndex: 'updated_at',
      key: 'updated_at',
      width: '40%',
      render: (time: string) => (
        <Text type="secondary">
          {dayjs(time).format('YYYY-MM-DD HH:mm:ss')}
        </Text>
      ),
    },
  ];

  return (
    <div className="dashboard-container">
      {/* 页面头部 */}
      <div className="page-header">
        <div className="header-content">
          <div className="header-info">
            <Title level={2} style={{ margin: 0, display: 'flex', alignItems: 'center' }}>
              <UserOutlined style={{ marginRight: 8, color: '#1890ff' }} />
              仪表盘
            </Title>
            <Text type="secondary">系统概览和统计信息</Text>
          </div>
          <div className="header-extra">
            <Space>
              <Badge
                color={stats.totalUsers && stats.totalUsers > 0 ? "green" : "blue"}
                text={`使用人数: ${stats.totalUsers || '加载中...'}`}
              />
              <Badge
                color="blue"
                text={`版本: ${systemVersion}`}
              />
              {updateAvailable && (
                <Badge
                  color="orange"
                  text={`有更新: ${stats.remoteVersion}`}
                  style={{ cursor: 'pointer' }}
                  onClick={() => {
                    window.open('http://xianyu.zhinianblog.cn', '_blank');
                  }}
                />
              )}
              <ReloadOutlined
                style={{ cursor: 'pointer', fontSize: 16 }}
                onClick={loadDashboardData}
                spin={loading}
              />
            </Space>
          </div>
        </div>
      </div>

      <Spin spinning={loading}>
        {/* 统计卡片 */}
        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
          <Col xs={24} sm={12} lg={6} xl={4}>
            <Card>
              <Statistic
                title="总账号数"
                value={stats.totalAccounts}
                prefix={<UserOutlined style={{ color: '#1890ff' }} />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6} xl={4}>
            <Card>
              <Statistic
                title="总关键词数"
                value={stats.totalKeywords}
                prefix={<MessageOutlined style={{ color: '#52c41a' }} />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6} xl={4}>
            <Card>
              <Statistic
                title="启用账号数"
                value={stats.activeAccounts}
                prefix={<CheckCircleOutlined style={{ color: '#faad14' }} />}
                valueStyle={{ color: '#faad14' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6} xl={4}>
            <Card>
              <Statistic
                title="总订单数"
                value={stats.totalOrders}
                prefix={<FileTextOutlined style={{ color: '#722ed1' }} />}
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6} xl={4}>
            <Card>
              <Statistic
                title="使用人数"
                value={stats.totalUsers || 0}
                prefix={<TeamOutlined style={{ color: '#eb2f96' }} />}
                valueStyle={{ color: '#eb2f96' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6} xl={4}>
            <Card>
              <Statistic
                title="系统版本"
                value={systemVersion}
                prefix={<FileTextOutlined style={{ color: '#13c2c2' }} />}
                valueStyle={{ color: '#13c2c2', fontSize: '16px' }}
                suffix={updateAvailable ? (
                  <Badge
                    color="orange"
                    text="有更新"
                    style={{ fontSize: '12px', marginLeft: '8px' }}
                  />
                ) : null}
              />
            </Card>
          </Col>
        </Row>

        {/* 账号详情列表 */}
        <Card
          title={
            <Space>
              <UserOutlined />
              <span>账号详情</span>
            </Space>
          }
          extra={
            <Text type="secondary">
              共 {stats.accounts.length} 个账号
            </Text>
          }
        >
          <Table
            columns={columns}
            dataSource={stats.accounts}
            rowKey="id"
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) =>
                `显示第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
            }}
            size="middle"
            scroll={{ x: 800 }}
          />
        </Card>
      </Spin>
    </div>
  );
};

export default Dashboard;
