/* 登录页面样式 */
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.login-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  z-index: 1;
}

.login-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
  animation: float 6s ease-in-out infinite;
}

.login-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(1px);
}

.login-content {
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 480px;
  padding: 20px;
}

.login-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.2);
  padding: 40px 40px;
  border: none;
}

.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.login-logo {
  margin-bottom: 16px;
  animation: pulse 2s ease-in-out infinite;
}

.login-footer {
  margin-top: 24px;
  text-align: center;
}

/* 登录方式选择样式 */
.login-card .ant-radio-group {
  display: flex;
  width: 100%;
}

.login-card .ant-radio-button-wrapper {
  flex: 1;
  text-align: center;
  border-radius: 6px !important;
  border: 1px solid #d9d9d9;
  margin: 0 2px;
  font-size: 13px;
  height: 36px;
  line-height: 34px;
}

.login-card .ant-radio-button-wrapper:first-child {
  margin-left: 0;
  border-radius: 6px 0 0 6px !important;
}

.login-card .ant-radio-button-wrapper:last-child {
  margin-right: 0;
  border-radius: 0 6px 6px 0 !important;
}

.login-card .ant-radio-button-wrapper-checked {
  background: #1890ff;
  border-color: #1890ff;
  color: white;
}

.login-card .ant-radio-button-wrapper-checked:hover {
  background: #40a9ff;
  border-color: #40a9ff;
}

/* 表单样式 */
.login-card .ant-form-item {
  margin-bottom: 20px;
}

.login-card .ant-input-affix-wrapper,
.login-card .ant-input {
  border-radius: 8px;
  border: 1px solid #e8e8e8;
  transition: all 0.3s ease;
}

.login-card .ant-input-affix-wrapper:hover,
.login-card .ant-input:hover {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

.login-card .ant-input-affix-wrapper:focus,
.login-card .ant-input-affix-wrapper-focused,
.login-card .ant-input:focus {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.login-card .ant-btn-primary {
  border-radius: 8px;
  height: 44px;
  font-size: 16px;
  font-weight: 500;
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
  transition: all 0.3s ease;
}

.login-card .ant-btn-primary:hover {
  background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
  box-shadow: 0 6px 16px rgba(24, 144, 255, 0.4);
  transform: translateY(-1px);
}

.login-card .ant-btn-primary:active {
  transform: translateY(0);
}

/* 动画效果 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-10px) rotate(1deg);
  }
  66% {
    transform: translateY(5px) rotate(-1deg);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-content {
    max-width: 420px;
    padding: 16px;
  }

  .login-card {
    padding: 32px 28px;
  }

  .login-card .ant-radio-button-wrapper {
    font-size: 12px;
    height: 32px;
    line-height: 30px;
  }
}

@media (max-width: 480px) {
  .login-content {
    max-width: 360px;
    padding: 12px;
  }

  .login-card {
    padding: 24px 20px;
  }

  .login-card .ant-radio-button-wrapper {
    font-size: 11px;
    height: 28px;
    line-height: 26px;
  }
}
