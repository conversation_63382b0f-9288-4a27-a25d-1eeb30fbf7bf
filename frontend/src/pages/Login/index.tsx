import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Card, Typography, Space, App, Radio, Divider, Image } from 'antd';
import { UserOutlined, LockOutlined, LoginOutlined, MailOutlined, SafetyOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { authApi } from '../../services/api';
import './index.css';

const { Title, Text } = Typography;

interface LoginForm {
  username?: string;
  password?: string;
  email?: string;
  verification_code?: string;
}

type LoginType = 'username' | 'email-password' | 'email-code';

const Login: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [loginType, setLoginType] = useState<LoginType>('username');
  const [registrationEnabled, setRegistrationEnabled] = useState(true);
  const [showDefaultLoginInfo, setShowDefaultLoginInfo] = useState(true);
  const [captchaImage, setCaptchaImage] = useState('');
  const [captchaVerified, setCaptchaVerified] = useState(false);
  const [sendingCode, setSendingCode] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const navigate = useNavigate();
  const { message } = App.useApp();
  const [form] = Form.useForm();
  const [emailForm] = Form.useForm();
  const [codeForm] = Form.useForm();

  // 初始化页面
  useEffect(() => {
    checkRegistrationStatus();
    checkLoginInfoStatus();
    checkExistingLogin();
  }, []);

  // 检查注册状态
  const checkRegistrationStatus = async () => {
    try {
      const response = await fetch('/registration-status');
      if (response.ok) {
        const data = await response.json();
        setRegistrationEnabled(data.enabled);
      }
    } catch (error) {
      console.error('检查注册状态失败:', error);
    }
  };

  // 检查默认登录信息显示状态
  const checkLoginInfoStatus = async () => {
    try {
      const response = await fetch('/login-info-status');
      if (response.ok) {
        const data = await response.json();
        setShowDefaultLoginInfo(data.enabled);
      }
    } catch (error) {
      console.error('检查登录信息显示状态失败:', error);
    }
  };

  // 检查是否已经登录
  const checkExistingLogin = async () => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      try {
        const response = await authApi.verify();
        if (response.data?.authenticated) {
          navigate('/dashboard');
        }
      } catch (error) {
        localStorage.removeItem('auth_token');
        localStorage.removeItem('user_info');
        localStorage.removeItem('user_role');
      }
    }
  };

  // 生成验证码
  const generateCaptcha = async () => {
    try {
      const sessionId = 'session_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
      const response = await fetch('/generate-captcha', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ session_id: sessionId })
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setCaptchaImage(data.captcha_image);
          setCaptchaVerified(false);
        }
      }
    } catch (error) {
      console.error('生成验证码失败:', error);
    }
  };

  // 发送邮箱验证码
  const sendEmailCode = async () => {
    const email = codeForm.getFieldValue('email');
    if (!email) {
      message.error('请输入邮箱地址');
      return;
    }

    if (!captchaVerified) {
      message.error('请先验证图形验证码');
      return;
    }

    try {
      setSendingCode(true);
      const response = await fetch('/send-verification-code', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email })
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          message.success('验证码已发送到您的邮箱');
          setCountdown(60);
          const timer = setInterval(() => {
            setCountdown(prev => {
              if (prev <= 1) {
                clearInterval(timer);
                return 0;
              }
              return prev - 1;
            });
          }, 1000);
        } else {
          message.error(data.message || '发送验证码失败');
        }
      }
    } catch (error) {
      message.error('发送验证码失败');
    } finally {
      setSendingCode(false);
    }
  };

  const handleLogin = async (values: LoginForm) => {
    try {
      setLoading(true);

      let loginData: any = {};

      if (loginType === 'username') {
        loginData = { username: values.username, password: values.password };
      } else if (loginType === 'email-password') {
        loginData = { email: values.email, password: values.password };
      } else if (loginType === 'email-code') {
        loginData = { email: values.email, verification_code: values.verification_code };
      }

      const response = await fetch('/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(loginData)
      });

      const data = await response.json();

      if (data.success) {
        // 保存登录状态
        localStorage.setItem('auth_token', data.token);

        // 保存用户信息
        const userInfo = {
          user_id: data.user_id,
          username: data.username,
          is_admin: data.is_admin
        };
        localStorage.setItem('user_info', JSON.stringify(userInfo));

        // 保存用户角色（用于菜单权限控制）
        localStorage.setItem('user_role', data.is_admin ? 'admin' : 'user');

        message.success('登录成功！');

        // 检查是否有重定向URL
        const redirectUrl = localStorage.getItem('redirectAfterLogin');
        if (redirectUrl) {
          localStorage.removeItem('redirectAfterLogin');
          window.location.href = redirectUrl;
        } else {
          navigate('/dashboard');
        }
      } else {
        message.error(data.message || '登录失败');
      }
    } catch (error: any) {
      console.error('登录失败:', error);
      message.error('登录失败，请检查网络连接');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="login-container">
      <div className="login-background">
        <div className="login-overlay" />
      </div>
      
      <div className="login-content">
        <Card className="login-card" bordered={false}>
          <div className="login-header">
            <div className="login-logo">
              <LoginOutlined style={{ fontSize: 48, color: '#1890ff' }} />
            </div>
            <Title level={2} style={{ textAlign: 'center', marginBottom: 8 }}>
              闲鱼自动回复系统
            </Title>
            <Text type="secondary" style={{ display: 'block', textAlign: 'center', marginBottom: 32 }}>
              智能客服，高效管理
            </Text>
          </div>

          {/* 登录方式选择 */}
          <div style={{ marginBottom: 24 }}>
            <Radio.Group
              value={loginType}
              onChange={(e) => {
                setLoginType(e.target.value);
                if (e.target.value === 'email-code') {
                  generateCaptcha();
                }
              }}
              style={{ width: '100%' }}
            >
              <Space direction="vertical" style={{ width: '100%' }}>
                <Radio value="username">用户名密码登录</Radio>
                <Radio value="email-password">邮箱密码登录</Radio>
                <Radio value="email-code">邮箱验证码登录</Radio>
              </Space>
            </Radio.Group>
          </div>

          <Divider />

          {/* 用户名密码登录 */}
          {loginType === 'username' && (
            <Form
              form={form}
              name="username-login"
              onFinish={handleLogin}
              autoComplete="off"
              size="large"
            >
              <Form.Item
                name="username"
                rules={[
                  { required: true, message: '请输入用户名' },
                  { min: 3, message: '用户名至少3个字符' }
                ]}
              >
                <Input
                  prefix={<UserOutlined />}
                  placeholder="用户名"
                  autoComplete="username"
                />
              </Form.Item>

              <Form.Item
                name="password"
                rules={[
                  { required: true, message: '请输入密码' },
                  { min: 6, message: '密码至少6个字符' }
                ]}
              >
                <Input.Password
                  prefix={<LockOutlined />}
                  placeholder="密码"
                  autoComplete="current-password"
                />
              </Form.Item>

              <Form.Item style={{ marginBottom: 16 }}>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                  block
                  icon={<LoginOutlined />}
                >
                  登录
                </Button>
              </Form.Item>
            </Form>
          )}

          {/* 邮箱密码登录 */}
          {loginType === 'email-password' && (
            <Form
              form={emailForm}
              name="email-login"
              onFinish={handleLogin}
              autoComplete="off"
              size="large"
            >
              <Form.Item
                name="email"
                rules={[
                  { required: true, message: '请输入邮箱地址' },
                  { type: 'email', message: '请输入有效的邮箱地址' }
                ]}
              >
                <Input
                  prefix={<MailOutlined />}
                  placeholder="邮箱地址"
                  autoComplete="email"
                />
              </Form.Item>

              <Form.Item
                name="password"
                rules={[
                  { required: true, message: '请输入密码' },
                  { min: 6, message: '密码至少6个字符' }
                ]}
              >
                <Input.Password
                  prefix={<LockOutlined />}
                  placeholder="密码"
                  autoComplete="current-password"
                />
              </Form.Item>

              <Form.Item style={{ marginBottom: 16 }}>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                  block
                  icon={<LoginOutlined />}
                >
                  登录
                </Button>
              </Form.Item>
            </Form>
          )}

          {/* 邮箱验证码登录 */}
          {loginType === 'email-code' && (
            <Form
              form={codeForm}
              name="code-login"
              onFinish={handleLogin}
              autoComplete="off"
              size="large"
            >
              <Form.Item
                name="email"
                rules={[
                  { required: true, message: '请输入邮箱地址' },
                  { type: 'email', message: '请输入有效的邮箱地址' }
                ]}
              >
                <Input
                  prefix={<MailOutlined />}
                  placeholder="邮箱地址"
                  autoComplete="email"
                />
              </Form.Item>

              {/* 图形验证码 */}
              {captchaImage && (
                <Form.Item
                  name="captcha"
                  rules={[{ required: true, message: '请输入图形验证码' }]}
                >
                  <Space.Compact style={{ width: '100%' }}>
                    <Input
                      prefix={<SafetyOutlined />}
                      placeholder="图形验证码"
                      maxLength={4}
                      onChange={(e) => {
                        if (e.target.value.length === 4) {
                          setCaptchaVerified(true);
                        }
                      }}
                    />
                    <Image
                      src={captchaImage}
                      alt="验证码"
                      width={100}
                      height={40}
                      preview={false}
                      style={{ cursor: 'pointer' }}
                      onClick={generateCaptcha}
                    />
                  </Space.Compact>
                </Form.Item>
              )}

              <Form.Item
                name="verification_code"
                rules={[
                  { required: true, message: '请输入邮箱验证码' },
                  { len: 6, message: '验证码为6位数字' }
                ]}
              >
                <Space.Compact style={{ width: '100%' }}>
                  <Input
                    prefix={<MailOutlined />}
                    placeholder="邮箱验证码"
                    maxLength={6}
                  />
                  <Button
                    onClick={sendEmailCode}
                    loading={sendingCode}
                    disabled={countdown > 0 || !captchaVerified}
                  >
                    {countdown > 0 ? `${countdown}s` : '发送验证码'}
                  </Button>
                </Space.Compact>
              </Form.Item>

              <Form.Item style={{ marginBottom: 16 }}>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                  block
                  icon={<LoginOutlined />}
                >
                  登录
                </Button>
              </Form.Item>
            </Form>
          )}

          <div className="login-footer">
            <Space direction="vertical" style={{ width: '100%', textAlign: 'center' }}>
              {showDefaultLoginInfo && (
                <Text type="secondary" style={{ fontSize: 12 }}>
                  默认账号: admin / 密码: admin123
                </Text>
              )}

              {registrationEnabled && (
                <div>
                  <Text type="secondary" style={{ fontSize: 12 }}>
                    还没有账号？
                  </Text>
                  <Button
                    type="link"
                    size="small"
                    onClick={() => navigate('/register')}
                    style={{ padding: '0 4px' }}
                  >
                    立即注册
                  </Button>
                </div>
              )}

              <Text type="secondary" style={{ fontSize: 12 }}>
                © 2024 闲鱼自动回复系统. All rights reserved.
              </Text>
            </Space>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default Login;
