import React, { useState, useEffect, useRef } from 'react';
import {
  <PERSON>,
  Typography,
  Button,
  Select,
  Switch,
  Space,
  Tag,
  Row,
  Col,
  Alert,
  App,
} from 'antd';
import {
  FileTextOutlined,
  ReloadOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  InfoCircleOutlined,
} from '@ant-design/icons';
import { logApi } from '../../services/api';
import type { LogEntry, LogLevel } from '../../types';
// import './SystemLogs.css';

const { Title, Text } = Typography;
const { Option } = Select;

interface SystemLogsProps {}

const SystemLogs: React.FC<SystemLogsProps> = () => {
  const { message } = App.useApp();
  const [loading, setLoading] = useState(false);
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [logLines, setLogLines] = useState<number>(100);
  const [logLevel, setLogLevel] = useState<LogLevel | ''>('');
  const [autoRefresh, setAutoRefresh] = useState<boolean>(false);
  const [logInfo, setLogInfo] = useState({
    fileName: '-',
    displayLines: '-',
    lastUpdate: '-',
  });

  const autoRefreshIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const terminalRef = useRef<HTMLDivElement>(null);

  // 加载系统日志
  const loadSystemLogs = async () => {
    try {
      setLoading(true);
      const response = await logApi.getSystemLogs({
        lines: logLines,
        level: logLevel || undefined,
      });

      if (response.success) {
        // 处理后端返回的日志数据
        const logs = response.data?.logs || response.logs || [];
        setLogs(logs);
        setLogInfo({
          fileName: response.data?.fileName || response.data?.log_file || 'system.log',
          displayLines: (response.data?.displayLines || response.data?.total_lines || logs.length).toString(),
          lastUpdate: new Date().toLocaleString(),
        });

        // 自动滚动到底部
        setTimeout(() => {
          if (terminalRef.current) {
            terminalRef.current.scrollTop = terminalRef.current.scrollHeight;
          }
        }, 100);
      }
    } catch (error: any) {
      console.error('加载系统日志失败:', error);
      message.error('加载系统日志失败');
    } finally {
      setLoading(false);
    }
  };

  // 切换自动刷新
  const toggleAutoRefresh = (checked: boolean) => {
    setAutoRefresh(checked);

    if (checked) {
      // 开启自动刷新，每5秒刷新一次
      autoRefreshIntervalRef.current = setInterval(() => {
        loadSystemLogs();
      }, 5000);
      message.success('已开启自动刷新 (5秒间隔)');
    } else {
      // 关闭自动刷新
      if (autoRefreshIntervalRef.current) {
        clearInterval(autoRefreshIntervalRef.current);
        autoRefreshIntervalRef.current = null;
      }
      message.info('已关闭自动刷新');
    }
  };

  // 过滤日志级别
  const filterLogsByLevel = (level: LogLevel | '') => {
    setLogLevel(level);
  };

  // 获取日志级别标签
  const getLogLevelTag = (level: LogLevel) => {
    const levelConfig = {
      info: { color: 'blue', text: 'INFO' },
      warning: { color: 'orange', text: 'WARNING' },
      error: { color: 'red', text: 'ERROR' },
      debug: { color: 'default', text: 'DEBUG' },
    };

    const config = levelConfig[level] || { color: 'default', text: level.toUpperCase() };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 格式化日志内容 - 终端样式
  const formatLogContent = (log: LogEntry) => {
    // 格式化时间戳
    const formatTimestamp = (timestamp: string) => {
      try {
        const date = new Date(timestamp);
        return date.toLocaleString('zh-CN', {
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
          hour12: false
        });
      } catch {
        return timestamp;
      }
    };

    // 获取日志级别颜色
    const getLevelColor = (level: string) => {
      switch (level.toUpperCase()) {
        case 'ERROR': return '#ff4d4f';
        case 'WARNING': return '#faad14';
        case 'INFO': return '#52c41a';
        case 'DEBUG': return '#1890ff';
        default: return '#d9d9d9';
      }
    };

    return (
      <div
        className="terminal-log-line"
        style={{
          fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
          fontSize: '13px',
          lineHeight: '1.4',
          padding: '2px 0',
          color: '#e6e6e6',
          whiteSpace: 'pre-wrap',
          wordBreak: 'break-word'
        }}
      >
        <span style={{ color: '#666', marginRight: '8px' }}>
          {formatTimestamp(log.timestamp)}
        </span>
        <span
          style={{
            color: getLevelColor(log.level),
            fontWeight: 'bold',
            marginRight: '8px',
            minWidth: '60px',
            display: 'inline-block'
          }}
        >
          [{log.level.toUpperCase()}]
        </span>
        {log.source && (
          <span style={{ color: '#1890ff', marginRight: '8px' }}>
            [{log.source}]
          </span>
        )}
        {log.function && (
          <span style={{ color: '#722ed1', marginRight: '8px' }}>
            {log.function}:{log.line}
          </span>
        )}
        <span style={{ color: '#e6e6e6' }}>
          {log.message}
        </span>
      </div>
    );
  };

  useEffect(() => {
    loadSystemLogs();

    // 组件卸载时清理定时器
    return () => {
      if (autoRefreshIntervalRef.current) {
        clearInterval(autoRefreshIntervalRef.current);
      }
    };
  }, []);

  // 当日志行数或级别改变时重新加载
  useEffect(() => {
    loadSystemLogs();
  }, [logLines, logLevel]);

  return (
    <div className="system-logs" style={{ height: '100vh', display: 'flex', flexDirection: 'column', overflow: 'hidden' }}>
      {/* 页面头部 */}
      <div className="page-header" style={{ flexShrink: 0, padding: '16px 0 8px 0' }}>
        <div className="header-content">
          <Title level={2} style={{ margin: 0, marginBottom: '4px' }}>
            <FileTextOutlined style={{ marginRight: 8 }} />
            系统日志
          </Title>
          <Text type="secondary">实时显示系统日志，支持自动刷新和统计分析</Text>
        </div>
      </div>

      {/* 日志控制面板 */}
      <Card title="日志控制" style={{ marginBottom: 12, flexShrink: 0 }}>
        <Row gutter={16} align="middle">
          <Col xs={24} sm={12} md={6}>
            <div>
              <Text strong>显示行数</Text>
              <Select
                value={logLines}
                onChange={setLogLines}
                style={{ width: '100%', marginTop: 8 }}
              >
                <Option value={50}>50行</Option>
                <Option value={100}>100行</Option>
                <Option value={200}>200行</Option>
                <Option value={500}>500行</Option>
                <Option value={1000}>1000行</Option>
              </Select>
            </div>
          </Col>

          <Col xs={24} sm={12} md={8}>
            <div>
              <Text strong>日志级别过滤</Text>
              <div style={{ marginTop: 8 }}>
                <Space wrap>
                  <Tag.CheckableTag
                    checked={logLevel === ''}
                    onChange={() => filterLogsByLevel('')}
                  >
                    全部
                  </Tag.CheckableTag>
                  <Tag.CheckableTag
                    checked={logLevel === 'info'}
                    onChange={() => filterLogsByLevel('info')}
                  >
                    INFO
                  </Tag.CheckableTag>
                  <Tag.CheckableTag
                    checked={logLevel === 'warning'}
                    onChange={() => filterLogsByLevel('warning')}
                  >
                    WARNING
                  </Tag.CheckableTag>
                  <Tag.CheckableTag
                    checked={logLevel === 'error'}
                    onChange={() => filterLogsByLevel('error')}
                  >
                    ERROR
                  </Tag.CheckableTag>
                  <Tag.CheckableTag
                    checked={logLevel === 'debug'}
                    onChange={() => filterLogsByLevel('debug')}
                  >
                    DEBUG
                  </Tag.CheckableTag>
                </Space>
              </div>
            </div>
          </Col>

          <Col xs={24} sm={12} md={6}>
            <div>
              <Text strong>自动刷新</Text>
              <div style={{ marginTop: 8 }}>
                <Space>
                  <Switch
                    checked={autoRefresh}
                    onChange={toggleAutoRefresh}
                    checkedChildren={<PlayCircleOutlined />}
                    unCheckedChildren={<PauseCircleOutlined />}
                  />
                  <Text type="secondary">
                    {autoRefresh ? '开启 (5s)' : '关闭'}
                  </Text>
                </Space>
              </div>
            </div>
          </Col>

          <Col xs={24} sm={12} md={4}>
            <div>
              <Text strong>&nbsp;</Text>
              <div style={{ marginTop: 8 }}>
                <Button
                  type="primary"
                  icon={<ReloadOutlined />}
                  onClick={loadSystemLogs}
                  loading={loading}
                  block
                >
                  刷新
                </Button>
              </div>
            </div>
          </Col>
        </Row>
      </Card>

      {/* 日志信息 */}
      <Card
        title={
          <Space>
            <InfoCircleOutlined />
            <span>日志信息</span>
          </Space>
        }
        extra={
          <Text type="secondary">
            最后更新: {logInfo.lastUpdate}
          </Text>
        }
        style={{ marginBottom: 12, flexShrink: 0 }}
      >
        <Row gutter={16}>
          <Col xs={24} sm={8}>
            <Text strong>日志文件: </Text>
            <Text type="secondary">{logInfo.fileName}</Text>
          </Col>
          <Col xs={24} sm={8}>
            <Text strong>显示行数: </Text>
            <Text type="secondary">{logInfo.displayLines}</Text>
          </Col>
          <Col xs={24} sm={8}>
            <Text strong>当前过滤: </Text>
            <Text type="secondary">
              {logLevel ? logLevel.toUpperCase() : '全部'}
            </Text>
          </Col>
        </Row>
      </Card>

      {/* 日志内容 - 终端样式 */}
      <Card
        title={
          <Space>
            <FileTextOutlined />
            <span>系统日志终端</span>
          </Space>
        }
        extra={
          <Space>
            <Text type="secondary">共 {logs.length} 条日志</Text>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              实时更新
            </Text>
          </Space>
        }
        style={{
          flex: 1,
          minHeight: 0,
          display: 'flex',
          flexDirection: 'column'
        }}
        bodyStyle={{
          flex: 1,
          overflow: 'hidden',
          padding: '0',
          display: 'flex',
          flexDirection: 'column'
        }}
      >
        <div
          ref={terminalRef}
          className="terminal-container"
          style={{
            height: '100%',
            backgroundColor: '#1e1e1e',
            color: '#e6e6e6',
            fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
            fontSize: '13px',
            overflow: 'auto',
            padding: '12px',
            border: '1px solid #333',
            borderRadius: '4px',
            scrollBehavior: 'smooth'
          }}
        >
          {loading ? (
            <div style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              height: '100%',
              color: '#52c41a'
            }}>
              <span>正在加载日志数据...</span>
            </div>
          ) : logs.length > 0 ? (
            <div className="terminal-log-content">
              {logs.map((log, index) => (
                <div key={index}>
                  {formatLogContent(log)}
                </div>
              ))}
              {/* 终端光标 */}
              <div style={{
                marginTop: '8px',
                color: '#52c41a',
                animation: 'blink 1s infinite'
              }}>
                <span>$</span>
                <span style={{
                  backgroundColor: '#52c41a',
                  color: '#1e1e1e',
                  marginLeft: '4px',
                  padding: '0 2px'
                }}>
                  _
                </span>
              </div>
            </div>
          ) : (
            <div style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              height: '100%',
              flexDirection: 'column',
              color: '#666'
            }}>
              <div style={{ marginBottom: '8px' }}>📄</div>
              <div>暂无日志数据</div>
              <div style={{ fontSize: '12px', marginTop: '4px' }}>
                当前过滤条件下没有找到日志记录
              </div>
            </div>
          )}
        </div>
      </Card>

      {/* 添加CSS动画 */}
      <style jsx>{`
        @keyframes blink {
          0%, 50% { opacity: 1; }
          51%, 100% { opacity: 0; }
        }
      `}</style>
    </div>
  );
};

export default SystemLogs;
