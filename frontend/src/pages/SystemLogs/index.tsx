import React, { useState, useEffect, useRef } from 'react';
import {
  <PERSON>,
  Typography,
  Button,
  Select,
  Switch,
  Space,
  Tag,
  Row,
  Col,
  Alert,
  App,
} from 'antd';
import {
  FileTextOutlined,
  ReloadOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  InfoCircleOutlined,
} from '@ant-design/icons';
import { logApi } from '../../services/api';
import type { LogEntry, LogLevel } from '../../types';
// import './SystemLogs.css';

const { Title, Text } = Typography;
const { Option } = Select;

interface SystemLogsProps {}

const SystemLogs: React.FC<SystemLogsProps> = () => {
  const { message } = App.useApp();
  const [loading, setLoading] = useState(false);
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [logLines, setLogLines] = useState<number>(100);
  const [logLevel, setLogLevel] = useState<LogLevel | ''>('');
  const [autoRefresh, setAutoRefresh] = useState<boolean>(false);
  const [logInfo, setLogInfo] = useState({
    fileName: '-',
    displayLines: '-',
    lastUpdate: '-',
  });

  const autoRefreshIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // 加载系统日志
  const loadSystemLogs = async () => {
    try {
      setLoading(true);
      const response = await logApi.getSystemLogs({
        lines: logLines,
        level: logLevel || undefined,
      });

      if (response.success) {
        // 处理后端返回的日志数据
        const logs = response.data?.logs || response.logs || [];
        setLogs(logs);
        setLogInfo({
          fileName: response.data?.fileName || response.data?.log_file || 'system.log',
          displayLines: (response.data?.displayLines || response.data?.total_lines || logs.length).toString(),
          lastUpdate: new Date().toLocaleString(),
        });
      }
    } catch (error: any) {
      console.error('加载系统日志失败:', error);
      message.error('加载系统日志失败');
    } finally {
      setLoading(false);
    }
  };

  // 切换自动刷新
  const toggleAutoRefresh = (checked: boolean) => {
    setAutoRefresh(checked);

    if (checked) {
      // 开启自动刷新，每5秒刷新一次
      autoRefreshIntervalRef.current = setInterval(() => {
        loadSystemLogs();
      }, 5000);
      message.success('已开启自动刷新 (5秒间隔)');
    } else {
      // 关闭自动刷新
      if (autoRefreshIntervalRef.current) {
        clearInterval(autoRefreshIntervalRef.current);
        autoRefreshIntervalRef.current = null;
      }
      message.info('已关闭自动刷新');
    }
  };

  // 过滤日志级别
  const filterLogsByLevel = (level: LogLevel | '') => {
    setLogLevel(level);
  };

  // 获取日志级别标签
  const getLogLevelTag = (level: LogLevel) => {
    const levelConfig = {
      info: { color: 'blue', text: 'INFO' },
      warning: { color: 'orange', text: 'WARNING' },
      error: { color: 'red', text: 'ERROR' },
      debug: { color: 'default', text: 'DEBUG' },
    };

    const config = levelConfig[level] || { color: 'default', text: level.toUpperCase() };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 格式化日志内容
  const formatLogContent = (log: LogEntry) => {
    // 格式化时间戳
    const formatTimestamp = (timestamp: string) => {
      try {
        const date = new Date(timestamp);
        return date.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
          hour12: false
        });
      } catch {
        return timestamp;
      }
    };

    return (
      <div className="log-entry" style={{ marginBottom: 8, padding: 8, border: '1px solid #f0f0f0', borderRadius: 4 }}>
        <div className="log-header" style={{ marginBottom: 4 }}>
          <Space>
            {getLogLevelTag(log.level)}
            <Text type="secondary" className="log-timestamp" style={{ fontSize: '12px' }}>
              {formatTimestamp(log.timestamp)}
            </Text>
            {log.source && (
              <Tag color="blue" style={{ fontSize: '11px' }}>
                {log.source}
              </Tag>
            )}
            {log.function && (
              <Text type="secondary" style={{ fontSize: '11px' }}>
                {log.function}:{log.line}
              </Text>
            )}
          </Space>
        </div>
        <div className="log-content">
          <Text className="log-message" style={{ fontFamily: 'monospace', fontSize: '13px', wordBreak: 'break-all' }}>
            {log.message}
          </Text>
        </div>
      </div>
    );
  };

  useEffect(() => {
    loadSystemLogs();

    // 组件卸载时清理定时器
    return () => {
      if (autoRefreshIntervalRef.current) {
        clearInterval(autoRefreshIntervalRef.current);
      }
    };
  }, []);

  // 当日志行数或级别改变时重新加载
  useEffect(() => {
    loadSystemLogs();
  }, [logLines, logLevel]);

  return (
    <div className="system-logs">
      {/* 页面头部 */}
      <div className="page-header">
        <div className="header-content">
          <Title level={2} style={{ margin: 0 }}>
            <FileTextOutlined style={{ marginRight: 8 }} />
            系统日志
          </Title>
          <Text type="secondary">实时显示系统日志，支持自动刷新和统计分析</Text>
        </div>
      </div>

      {/* 日志控制面板 */}
      <Card title="日志控制" style={{ marginBottom: 16 }}>
        <Row gutter={16} align="middle">
          <Col xs={24} sm={12} md={6}>
            <div>
              <Text strong>显示行数</Text>
              <Select
                value={logLines}
                onChange={setLogLines}
                style={{ width: '100%', marginTop: 8 }}
              >
                <Option value={50}>50行</Option>
                <Option value={100}>100行</Option>
                <Option value={200}>200行</Option>
                <Option value={500}>500行</Option>
                <Option value={1000}>1000行</Option>
              </Select>
            </div>
          </Col>

          <Col xs={24} sm={12} md={8}>
            <div>
              <Text strong>日志级别过滤</Text>
              <div style={{ marginTop: 8 }}>
                <Space wrap>
                  <Tag.CheckableTag
                    checked={logLevel === ''}
                    onChange={() => filterLogsByLevel('')}
                  >
                    全部
                  </Tag.CheckableTag>
                  <Tag.CheckableTag
                    checked={logLevel === 'info'}
                    onChange={() => filterLogsByLevel('info')}
                  >
                    INFO
                  </Tag.CheckableTag>
                  <Tag.CheckableTag
                    checked={logLevel === 'warning'}
                    onChange={() => filterLogsByLevel('warning')}
                  >
                    WARNING
                  </Tag.CheckableTag>
                  <Tag.CheckableTag
                    checked={logLevel === 'error'}
                    onChange={() => filterLogsByLevel('error')}
                  >
                    ERROR
                  </Tag.CheckableTag>
                  <Tag.CheckableTag
                    checked={logLevel === 'debug'}
                    onChange={() => filterLogsByLevel('debug')}
                  >
                    DEBUG
                  </Tag.CheckableTag>
                </Space>
              </div>
            </div>
          </Col>

          <Col xs={24} sm={12} md={6}>
            <div>
              <Text strong>自动刷新</Text>
              <div style={{ marginTop: 8 }}>
                <Space>
                  <Switch
                    checked={autoRefresh}
                    onChange={toggleAutoRefresh}
                    checkedChildren={<PlayCircleOutlined />}
                    unCheckedChildren={<PauseCircleOutlined />}
                  />
                  <Text type="secondary">
                    {autoRefresh ? '开启 (5s)' : '关闭'}
                  </Text>
                </Space>
              </div>
            </div>
          </Col>

          <Col xs={24} sm={12} md={4}>
            <div>
              <Text strong>&nbsp;</Text>
              <div style={{ marginTop: 8 }}>
                <Button
                  type="primary"
                  icon={<ReloadOutlined />}
                  onClick={loadSystemLogs}
                  loading={loading}
                  block
                >
                  刷新
                </Button>
              </div>
            </div>
          </Col>
        </Row>
      </Card>

      {/* 日志信息 */}
      <Card
        title={
          <Space>
            <InfoCircleOutlined />
            <span>日志信息</span>
          </Space>
        }
        extra={
          <Text type="secondary">
            最后更新: {logInfo.lastUpdate}
          </Text>
        }
        style={{ marginBottom: 16 }}
      >
        <Row gutter={16}>
          <Col xs={24} sm={8}>
            <Text strong>日志文件: </Text>
            <Text type="secondary">{logInfo.fileName}</Text>
          </Col>
          <Col xs={24} sm={8}>
            <Text strong>显示行数: </Text>
            <Text type="secondary">{logInfo.displayLines}</Text>
          </Col>
          <Col xs={24} sm={8}>
            <Text strong>当前过滤: </Text>
            <Text type="secondary">
              {logLevel ? logLevel.toUpperCase() : '全部'}
            </Text>
          </Col>
        </Row>
      </Card>

      {/* 日志内容 */}
      <Card
        title={
          <Space>
            <FileTextOutlined />
            <span>日志内容</span>
          </Space>
        }
        extra={
          <Text type="secondary">
            共 {logs.length} 条日志
          </Text>
        }
      >
        <div className="log-container">
          {loading ? (
            <div className="log-loading">
              <Text type="secondary">正在加载日志...</Text>
            </div>
          ) : logs.length > 0 ? (
            <div className="log-list">
              {logs.map((log, index) => (
                <div key={index} className="log-item">
                  {formatLogContent(log)}
                </div>
              ))}
            </div>
          ) : (
            <div className="log-empty">
              <Alert
                message="暂无日志"
                description="当前过滤条件下没有找到日志记录"
                type="info"
                showIcon
              />
            </div>
          )}
        </div>
      </Card>
    </div>
  );
};

export default SystemLogs;
