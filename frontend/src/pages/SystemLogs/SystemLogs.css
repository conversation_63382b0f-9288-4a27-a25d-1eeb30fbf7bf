.system-logs {
  padding: 0;
  min-height: auto;
}

.page-header {
  margin-bottom: 24px;
}

.header-content h2 {
  margin-bottom: 8px !important;
}

.log-container {
  max-height: 600px;
  overflow-y: auto;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  background: #fafafa;
}

.log-loading {
  padding: 40px;
  text-align: center;
}

.log-list {
  padding: 0;
}

.log-item {
  border-bottom: 1px solid #f0f0f0;
  background: #fff;
  transition: background-color 0.2s;
}

.log-item:hover {
  background: #f9f9f9;
}

.log-item:last-child {
  border-bottom: none;
}

.log-entry {
  padding: 12px 16px;
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.log-timestamp {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
}

.log-content {
  margin-left: 0;
}

.log-message {
  font-family: 'Monaco', 'Men<PERSON>', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-break: break-all;
  background: transparent !important;
  border: none !important;
  padding: 0 !important;
  color: #333;
}

.log-empty {
  padding: 40px;
}

/* 日志级别标签样式 */
.ant-tag {
  font-weight: 500;
  border-radius: 4px;
}

/* 自动刷新指示器 */
.auto-refresh-indicator {
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .log-container {
    max-height: 400px;
  }
  
  .log-entry {
    padding: 8px 12px;
  }
  
  .log-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .log-message {
    font-size: 12px;
  }
}

/* 滚动条样式 */
.log-container::-webkit-scrollbar {
  width: 8px;
}

.log-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.log-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.log-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 日志级别过滤标签样式 */
.ant-tag-checkable {
  cursor: pointer;
  transition: all 0.2s;
}

.ant-tag-checkable:hover {
  opacity: 0.8;
}

.ant-tag-checkable-checked {
  background-color: #1890ff !important;
  border-color: #1890ff !important;
  color: #fff !important;
}
