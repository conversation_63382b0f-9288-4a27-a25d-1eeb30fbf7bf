import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Button,
  Table,
  Space,
  Tag,
  Popconfirm,
  Row,
  Col,
  Statistic,
  App,
} from 'antd';
import {
  UserOutlined,
  DeleteOutlined,
  ReloadOutlined,
  TeamOutlined,
  DatabaseOutlined,
  CreditCardOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import { userApi } from '../../services/api';
import type { User, UserStats } from '../../types';

const { Title, Text } = Typography;

interface UserManagementProps {}

const UserManagement: React.FC<UserManagementProps> = () => {
  const { message } = App.useApp();
  const [loading, setLoading] = useState(false);
  const [users, setUsers] = useState<User[]>([]);
  const [stats, setStats] = useState<UserStats>({
    totalUsers: 0,
    totalCookies: 0,
    totalCards: 0,
    systemStatus: 'running',
  });

  // 加载用户统计信息
  const loadUserStats = async () => {
    try {
      const response = await userApi.getUserStats();
      if (response.success) {
        setStats(response.data);
      }
    } catch (error) {
      console.error('加载用户统计失败:', error);
    }
  };

  // 加载用户列表
  const loadUsers = async () => {
    try {
      setLoading(true);
      const response = await userApi.getUsers();
      if (response.success) {
        setUsers(response.data?.users || []);
      }
    } catch (error: any) {
      console.error('加载用户列表失败:', error);
      message.error('加载用户列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 删除用户
  const handleDeleteUser = async (userId: string, username: string) => {
    if (username === 'admin') {
      message.warning('不能删除管理员账号');
      return;
    }

    try {
      const response = await userApi.deleteUser(userId);
      if (response.success) {
        message.success(`用户 "${username}" 删除成功`);
        loadUsers();
        loadUserStats();
      }
    } catch (error: any) {
      message.error(error.message || '删除用户失败');
    }
  };

  // 刷新数据
  const handleRefresh = async () => {
    await Promise.all([loadUserStats(), loadUsers()]);
    message.success('数据已刷新');
  };

  useEffect(() => {
    loadUserStats();
    loadUsers();
  }, []);

  const columns = [
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
      render: (text: string) => (
        <Space>
          <UserOutlined />
          <Text strong>{text}</Text>
          {text === 'admin' && <Tag color="red">管理员</Tag>}
        </Space>
      ),
    },
    {
      title: '用户ID',
      dataIndex: 'id',
      key: 'id',
      render: (text: string) => (
        <Text code copyable={{ text }}>
          {text}
        </Text>
      ),
    },
    {
      title: 'Cookie数量',
      dataIndex: 'cookie_count',
      key: 'cookie_count',
      render: (count: number) => (
        <Tag color={count > 0 ? 'success' : 'default'}>
          {count}
        </Tag>
      ),
    },
    {
      title: '卡券数量',
      dataIndex: 'card_count',
      key: 'card_count',
      render: (count: number) => (
        <Tag color={count > 0 ? 'processing' : 'default'}>
          {count}
        </Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (text: string) => (
        <Text type="secondary">
          {new Date(text).toLocaleString()}
        </Text>
      ),
    },
    {
      title: '最后登录',
      dataIndex: 'last_login',
      key: 'last_login',
      render: (text: string) => (
        <Text type="secondary">
          {text ? new Date(text).toLocaleString() : '从未登录'}
        </Text>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: User) => (
        <Space>
          {record.username !== 'admin' && (
            <Popconfirm
              title="确认删除"
              description={`确定要删除用户 "${record.username}" 吗？此操作不可恢复。`}
              onConfirm={() => handleDeleteUser(record.id, record.username)}
              okText="确认"
              cancelText="取消"
              icon={<ExclamationCircleOutlined style={{ color: 'red' }} />}
            >
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
                size="small"
              >
                删除
              </Button>
            </Popconfirm>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div className="user-management">
      {/* 页面头部 */}
      <div className="page-header">
        <div className="header-content">
          <Title level={2} style={{ margin: 0 }}>
            <TeamOutlined style={{ marginRight: 8 }} />
            用户管理
          </Title>
          <Text type="secondary">管理系统中的所有用户账号</Text>
        </div>
        <div className="header-extra">
          <Button
            icon={<ReloadOutlined />}
            onClick={handleRefresh}
            loading={loading}
          >
            刷新
          </Button>
        </div>
      </div>

      {/* 统计信息 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="总用户数"
              value={stats.totalUsers}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="总Cookie数"
              value={stats.totalCookies}
              prefix={<DatabaseOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="总卡券数"
              value={stats.totalCards}
              prefix={<CreditCardOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="系统状态"
              value={stats.systemStatus === 'running' ? '运行中' : '异常'}
              prefix={<CheckCircleOutlined />}
              valueStyle={{
                color: stats.systemStatus === 'running' ? '#52c41a' : '#ff4d4f'
              }}
            />
          </Card>
        </Col>
      </Row>

      {/* 用户列表 */}
      <Card
        title={
          <Space>
            <TeamOutlined />
            <span>用户列表</span>
          </Space>
        }
        extra={
          <Text type="secondary">
            共 {users.length} 个用户
          </Text>
        }
      >
        <Table
          columns={columns}
          dataSource={users}
          rowKey="id"
          loading={loading}
          pagination={{
            total: users.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
          scroll={{ x: 800 }}
        />
      </Card>
    </div>
  );
};

export default UserManagement;
