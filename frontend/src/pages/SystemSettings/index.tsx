import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  Select,
  Switch,
  Row,
  Col,
  Typography,
  Space,
  App,
  Upload,
  Alert
} from 'antd';
import {
  SettingOutlined,
  LockOutlined,
  BgColorsOutlined,
  UserAddOutlined,
  InfoCircleOutlined,
  DatabaseOutlined,
  DownloadOutlined,
  UploadOutlined,
  ExclamationCircleOutlined,
  MailOutlined,
  SafetyOutlined,
  KeyOutlined
} from '@ant-design/icons';
import { systemApi } from '../../services/api';

const { Title, Text } = Typography;
const { Option } = Select;

const SystemSettings: React.FC = () => {
  const { message } = App.useApp();
  const [passwordForm] = Form.useForm();
  const [themeForm] = Form.useForm();
  const [smtpForm] = Form.useForm();
  const [apiForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [registrationEnabled, setRegistrationEnabled] = useState(false);
  const [showDefaultLoginInfo, setShowDefaultLoginInfo] = useState(true);
  const [systemSettings, setSystemSettings] = useState<any>({});

  // 模拟加载设置
  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const response = await systemApi.getSettings();
      if (response.success) {
        const settings = response.data;
        setSystemSettings(settings);
        setRegistrationEnabled(settings.registration_enabled === 'true');
        setShowDefaultLoginInfo(settings.show_default_login_info === 'true');

        // 设置表单初始值
        smtpForm.setFieldsValue({
          smtp_server: settings.smtp_server || '',
          smtp_port: settings.smtp_port || '587',
          smtp_user: settings.smtp_user || '',
          smtp_password: settings.smtp_password || '',
          smtp_from: settings.smtp_from || '',
          smtp_use_tls: settings.smtp_use_tls === 'true',
          smtp_use_ssl: settings.smtp_use_ssl === 'true',
        });

        apiForm.setFieldsValue({
          qq_reply_secret_key: settings.qq_reply_secret_key || 'xianyu_qq_reply_2024',
        });
      }
    } catch (error) {
      console.error('加载设置失败:', error);
      message.error('加载系统设置失败');
    }
  };

  const handlePasswordChange = async (values: any) => {
    if (values.newPassword !== values.confirmPassword) {
      message.error('两次输入的密码不一致');
      return;
    }

    try {
      setLoading(true);
      // 调用修改密码API
      // await authApi.changePassword(values);
      message.success('密码修改成功，请重新登录');
      passwordForm.resetFields();
    } catch (error: any) {
      message.error(error.message || '密码修改失败');
    } finally {
      setLoading(false);
    }
  };

  const handleThemeChange = async (values: any) => {
    try {
      setLoading(true);
      // 调用主题设置API
      // await settingsApi.updateTheme(values);
      message.success('主题设置已保存');
    } catch (error: any) {
      message.error(error.message || '主题设置失败');
    } finally {
      setLoading(false);
    }
  };

  const handleRegistrationToggle = async (checked: boolean) => {
    try {
      setRegistrationEnabled(checked);
      // 调用注册设置API
      // await settingsApi.updateRegistrationSettings(checked);
      message.success(checked ? '用户注册已开启' : '用户注册已关闭');
    } catch (error: any) {
      message.error(error.message || '设置失败');
    }
  };

  const handleLoginInfoToggle = async (checked: boolean) => {
    try {
      setShowDefaultLoginInfo(checked);
      // 调用登录信息设置API
      // await settingsApi.updateLoginInfoSettings(checked);
      message.success(checked ? '默认登录信息已显示' : '默认登录信息已隐藏');
    } catch (error: any) {
      message.error(error.message || '设置失败');
    }
  };

  const handleDatabaseBackup = async () => {
    try {
      message.info('正在准备数据库备份，请稍候...');
      // 调用备份API
      // const response = await backupApi.downloadBackup();
      // 下载文件逻辑
      message.success('数据库备份下载成功');
    } catch (error: any) {
      message.error(error.message || '备份失败');
    }
  };

  const handleDatabaseRestore = async (file: any) => {
    try {
      message.info('正在恢复数据库，请稍候...');
      // 调用恢复API
      // await backupApi.uploadBackup(file);
      message.success('数据库恢复成功');
    } catch (error: any) {
      message.error(error.message || '恢复失败');
    }
  };

  // 保存SMTP设置
  const handleSMTPSave = async (values: any) => {
    try {
      setLoading(true);

      // 逐个保存SMTP配置项
      for (const [key, value] of Object.entries(values)) {
        await systemApi.updateSettings({
          key,
          value: String(value),
          description: `SMTP配置 - ${key}`
        });
      }

      message.success('SMTP配置保存成功');
      await loadSettings(); // 重新加载设置
    } catch (error: any) {
      message.error(error.message || 'SMTP配置保存失败');
    } finally {
      setLoading(false);
    }
  };

  // 保存API安全设置
  const handleAPISave = async (values: any) => {
    try {
      setLoading(true);

      // 保存QQ回复秘钥
      await systemApi.updateSettings({
        key: 'qq_reply_secret_key',
        value: values.qq_reply_secret_key,
        description: 'QQ回复消息API秘钥'
      });

      message.success('API安全设置保存成功');
      await loadSettings(); // 重新加载设置
    } catch (error: any) {
      message.error(error.message || 'API安全设置保存失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="system-settings">
      {/* 页面头部 */}
      <div className="page-header">
        <div className="header-content">
          <div className="header-info">
            <Title level={2} style={{ margin: 0, display: 'flex', alignItems: 'center' }}>
              <SettingOutlined style={{ marginRight: 8, color: '#1890ff' }} />
              系统设置
            </Title>
            <Text type="secondary">管理系统配置和用户设置</Text>
          </div>
        </div>
      </div>

      <Row gutter={[24, 24]}>
        {/* 密码设置 */}
        <Col xs={24} lg={12}>
          <Card
            title={
              <Space>
                <LockOutlined />
                密码设置
              </Space>
            }
          >
            <Form
              form={passwordForm}
              layout="vertical"
              onFinish={handlePasswordChange}
            >
              <Form.Item
                label="当前密码"
                name="currentPassword"
                rules={[{ required: true, message: '请输入当前密码' }]}
              >
                <Input.Password placeholder="请输入当前密码" />
              </Form.Item>

              <Form.Item
                label="新密码"
                name="newPassword"
                rules={[
                  { required: true, message: '请输入新密码' },
                  { min: 6, message: '密码长度至少6位' }
                ]}
              >
                <Input.Password placeholder="请输入新密码" />
              </Form.Item>

              <Form.Item
                label="确认新密码"
                name="confirmPassword"
                rules={[{ required: true, message: '请确认新密码' }]}
              >
                <Input.Password placeholder="请再次输入新密码" />
              </Form.Item>

              <Form.Item>
                <Button type="primary" htmlType="submit" loading={loading}>
                  更新密码
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </Col>

        {/* 主题设置 */}
        <Col xs={24} lg={12}>
          <Card
            title={
              <Space>
                <BgColorsOutlined />
                主题设置
              </Space>
            }
          >
            <Form
              form={themeForm}
              layout="vertical"
              onFinish={handleThemeChange}
              initialValues={{ themeColor: 'blue' }}
            >
              <Form.Item
                label="主题颜色"
                name="themeColor"
              >
                <Select placeholder="选择主题颜色">
                  <Option value="blue">蓝色</Option>
                  <Option value="green">绿色</Option>
                  <Option value="purple">紫色</Option>
                  <Option value="red">红色</Option>
                  <Option value="orange">橙色</Option>
                </Select>
              </Form.Item>

              <Form.Item>
                <Button type="primary" htmlType="submit" loading={loading}>
                  应用主题
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </Col>

        {/* 注册设置 */}
        <Col xs={24} lg={12}>
          <Card
            title={
              <Space>
                <UserAddOutlined />
                注册设置
                <span style={{
                  background: '#faad14',
                  color: 'white',
                  padding: '2px 8px',
                  borderRadius: '4px',
                  fontSize: '12px'
                }}>
                  管理员专用
                </span>
              </Space>
            }
          >
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Switch
                  checked={registrationEnabled}
                  onChange={handleRegistrationToggle}
                />
                <Text strong style={{ marginLeft: '8px' }}>开启用户注册</Text>
              </div>
              <Alert
                message="关闭后，新用户将无法注册账号，登录页面也不会显示注册链接"
                type="info"
                showIcon
                icon={<InfoCircleOutlined />}
              />
            </Space>
          </Card>
        </Col>

        {/* 登录信息设置 */}
        <Col xs={24} lg={12}>
          <Card
            title={
              <Space>
                <InfoCircleOutlined />
                登录信息设置
                <span style={{
                  background: '#faad14',
                  color: 'white',
                  padding: '2px 8px',
                  borderRadius: '4px',
                  fontSize: '12px'
                }}>
                  管理员专用
                </span>
              </Space>
            }
          >
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Switch
                  checked={showDefaultLoginInfo}
                  onChange={handleLoginInfoToggle}
                />
                <Text strong style={{ marginLeft: '8px' }}>显示默认登录信息</Text>
              </div>
              <Alert
                message="开启后，登录页面将显示默认的用户名和密码信息，方便用户快速登录"
                type="info"
                showIcon
                icon={<InfoCircleOutlined />}
              />
            </Space>
          </Card>
        </Col>

        {/* SMTP邮件配置 */}
        <Col xs={24}>
          <Card
            title={
              <Space>
                <MailOutlined />
                SMTP邮件配置
                <span style={{
                  background: '#1890ff',
                  color: 'white',
                  padding: '2px 8px',
                  borderRadius: '4px',
                  fontSize: '12px'
                }}>
                  邮件服务
                </span>
              </Space>
            }
          >
            <Alert
              message="配置SMTP服务器用于发送注册验证码等邮件通知"
              description="支持QQ邮箱、Gmail、163邮箱等主流邮箱服务商"
              type="info"
              showIcon
              style={{ marginBottom: '16px' }}
            />

            <Form
              form={smtpForm}
              layout="vertical"
              onFinish={handleSMTPSave}
            >
              <Row gutter={16}>
                <Col xs={24} md={12}>
                  <Form.Item
                    label="SMTP服务器"
                    name="smtp_server"
                    rules={[{ required: true, message: '请输入SMTP服务器地址' }]}
                  >
                    <Input placeholder="smtp.qq.com" />
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item
                    label="SMTP端口"
                    name="smtp_port"
                    rules={[{ required: true, message: '请输入SMTP端口' }]}
                  >
                    <Input placeholder="587" />
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item
                    label="发件邮箱"
                    name="smtp_user"
                    rules={[
                      { required: true, message: '请输入发件邮箱' },
                      { type: 'email', message: '请输入有效的邮箱地址' }
                    ]}
                  >
                    <Input placeholder="<EMAIL>" />
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item
                    label="邮箱密码/授权码"
                    name="smtp_password"
                    rules={[{ required: true, message: '请输入邮箱密码或授权码' }]}
                  >
                    <Input.Password placeholder="输入密码或授权码" />
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item
                    label="发件人显示名"
                    name="smtp_from"
                  >
                    <Input placeholder="留空则使用邮箱地址" />
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item label="安全设置">
                    <Space>
                      <Form.Item name="smtp_use_tls" valuePropName="checked" style={{ marginBottom: 0 }}>
                        <Switch size="small" />
                      </Form.Item>
                      <Text>启用TLS</Text>
                      <Form.Item name="smtp_use_ssl" valuePropName="checked" style={{ marginBottom: 0 }}>
                        <Switch size="small" />
                      </Form.Item>
                      <Text>启用SSL</Text>
                    </Space>
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item>
                <Button type="primary" htmlType="submit" loading={loading}>
                  保存SMTP配置
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </Col>

        {/* API安全设置 */}
        <Col xs={24}>
          <Card
            title={
              <Space>
                <SafetyOutlined />
                API安全设置
                <span style={{
                  background: '#ff4d4f',
                  color: 'white',
                  padding: '2px 8px',
                  borderRadius: '4px',
                  fontSize: '12px'
                }}>
                  安全配置
                </span>
              </Space>
            }
          >
            <Alert
              message="配置API接口的安全密钥，用于验证外部调用"
              description="修改密钥后，所有使用该密钥的外部应用需要同步更新"
              type="warning"
              showIcon
              style={{ marginBottom: '16px' }}
            />

            <Form
              form={apiForm}
              layout="vertical"
              onFinish={handleAPISave}
            >
              <Row gutter={16}>
                <Col xs={24} md={12}>
                  <Form.Item
                    label="QQ回复消息API密钥"
                    name="qq_reply_secret_key"
                    rules={[{ required: true, message: '请输入API密钥' }]}
                  >
                    <Input.Password
                      placeholder="xianyu_qq_reply_2024"
                      addonBefore={<KeyOutlined />}
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item>
                <Button type="primary" htmlType="submit" loading={loading}>
                  保存API设置
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </Col>

        {/* 数据库管理 */}
        <Col xs={24}>
          <Card
            title={
              <Space>
                <DatabaseOutlined />
                数据库管理
                <span style={{
                  background: '#ff4d4f',
                  color: 'white',
                  padding: '2px 8px',
                  borderRadius: '4px',
                  fontSize: '12px'
                }}>
                  危险操作
                </span>
              </Space>
            }
          >
            <Alert
              message="数据库操作具有风险性，请谨慎操作"
              description="备份和恢复操作会影响系统数据，建议在维护时间进行操作"
              type="warning"
              showIcon
              icon={<ExclamationCircleOutlined />}
              style={{ marginBottom: '16px' }}
            />

            <Row gutter={16}>
              <Col>
                <Button
                  type="primary"
                  icon={<DownloadOutlined />}
                  onClick={handleDatabaseBackup}
                >
                  下载数据库备份
                </Button>
              </Col>
              <Col>
                <Upload
                  accept=".sql,.db,.backup"
                  showUploadList={false}
                  beforeUpload={(file) => {
                    handleDatabaseRestore(file);
                    return false;
                  }}
                >
                  <Button
                    icon={<UploadOutlined />}
                    danger
                  >
                    恢复数据库备份
                  </Button>
                </Upload>
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default SystemSettings;
