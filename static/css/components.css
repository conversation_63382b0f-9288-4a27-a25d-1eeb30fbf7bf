/* ================================
   通用卡片样式 - 适用于所有菜单的卡片
   ================================ */

/* 旋转动画 */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.spin {
    animation: spin 1s linear infinite;
}
.card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  margin-bottom: 2rem;
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.card-header {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  color: white;
  border: none;
  border-radius: 16px 16px 0 0 !important;
  font-weight: 600;
  font-size: 1.1rem;
  padding: 1.25rem 1.5rem;
}

.card-body {
  padding: 1.5rem;
}

.btn {
  border-radius: 10px;
  font-weight: 500;
  transition: all 0.3s ease;
  padding: 0.5rem 1rem;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  border: none;
}

.btn-success {
  background: linear-gradient(135deg, var(--success-color), #059669);
  border: none;
}

.btn-danger {
  background: linear-gradient(135deg, var(--danger-color), #dc2626);
  border: none;
}

.btn-outline-primary {
  border: 2px solid var(--primary-color);
  color: var(--primary-color);
}

.btn-outline-primary:hover {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.btn-outline-success {
  border: 2px solid var(--success-color);
  color: var(--success-color);
}

.btn-outline-success:hover {
  background: var(--success-color);
  border-color: var(--success-color);
  color: white;
}

.btn-outline-info {
  border: 2px solid #0dcaf0;
  color: #0dcaf0;
}

.btn-outline-info:hover {
  background: #0dcaf0;
  border-color: #0dcaf0;
  color: white;
}

.btn-outline-danger {
  border: 2px solid var(--danger-color);
  color: var(--danger-color);
}

.btn-outline-danger:hover {
  background: var(--danger-color);
  border-color: var(--danger-color);
  color: white;
}

/* ================================
   通用表格样式 - 适用于所有菜单的表格
   ================================ */
.table {
  margin-bottom: 0;
  background: transparent;
}

.table th {
  border-top: none;
  border-bottom: 2px solid var(--border-color);
  font-weight: 600;
  color: var(--dark-color);
  background: rgba(79, 70, 229, 0.05);
  padding: 1rem;
}

.table td {
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
  vertical-align: middle;
}

.table tbody tr:hover {
  background: rgba(79, 70, 229, 0.02);
}

.badge {
  font-weight: 500;
  padding: 5px 10px;
}

.form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.cookie-value {
  font-family: 'JetBrains Mono', 'Fira Code', 'Courier New', monospace;
  font-size: 0.85rem;
  background: #f8fafc;
  padding: 0.75rem;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  word-break: break-all;
  line-height: 1.4;
  max-height: 120px;
  overflow-y: auto;
  white-space: pre-wrap;
}

.loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255,255,255,0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-spinner {
  width: 3rem;
  height: 3rem;
}

.keyword-editor {
  border-radius: 10px;
  box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

.modal-content {
  border-radius: 10px;
  border: none;
}

.modal-header {
  border-bottom: 1px solid rgba(0,0,0,0.05);
}

.modal-footer {
  border-top: 1px solid rgba(0,0,0,0.05);
}

.cookie-id {
  font-weight: 600;
  color: var(--primary-color);
  font-size: 1rem;
}

.empty-state {
  text-align: center;
  padding: 3rem 2rem;
  color: var(--secondary-color);
}

.empty-state i {
  opacity: 0.5;
}

.form-control, .form-select {
  border-radius: 10px;
  border: 2px solid var(--border-color);
  transition: all 0.3s ease;
  padding: 0.75rem 1rem;
}

.form-control:focus, .form-select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 4px rgba(79, 70, 229, 0.1);
  transform: translateY(-1px);
}

.form-label {
  font-weight: 600;
  color: var(--dark-color);
  margin-bottom: 0.5rem;
}

/* ================================
   通用模态框样式 - 适用于所有菜单的模态框
   ================================ */
.modal-content {
  border: none;
  border-radius: 16px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(10px);
}

.modal-header {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  color: white;
  border: none;
  border-radius: 16px 16px 0 0;
}

.toast-container {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 9999;
}

.toast {
  border: none;
  border-radius: 12px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(10px);
}

.btn-group .btn {
  margin-right: 0.25rem;
}

.btn-group .btn:last-child {
  margin-right: 0;
}

@media (max-width: 768px) {
  .container {
    margin-top: 1rem;
    padding: 0 1rem;
  }

  .card-body {
    padding: 1rem;
  }

  .btn {
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
  }

  .cookie-value {
    font-size: 0.75rem;
    max-height: 80px;
  }

  .btn-group {
    flex-direction: column;
  }

  .btn-group .btn {
    margin-right: 0;
    margin-bottom: 0.25rem;
  }

  /* 移动端商品表格优化 */
  #itemsTableBody .btn-group {
    flex-direction: row;
  }

  #itemsTableBody .btn-group .btn {
    padding: 0.2rem 0.4rem;
    font-size: 0.75rem;
  }
}

/* ================================
   API参数提示样式
   ================================ */
.param-item {
  display: flex;
  align-items: center;
  padding: 0.25rem 0;
}

.param-name {
  background-color: #f8f9fa;
  color: #495057;
  padding: 0.2rem 0.4rem;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 0.85rem;
  font-weight: 500;
  border: 1px solid #dee2e6;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 120px;
  display: inline-block;
}

.param-name:hover {
  background-color: #e9ecef;
  border-color: #adb5bd;
  transform: translateY(-1px);
}

#postParamsHelp,
#editPostParamsHelp {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 1rem;
}

#postParamsHelp .alert,
#editPostParamsHelp .alert {
  margin-bottom: 0;
  border-radius: 6px;
}

/* 参数项点击效果 */
.param-name:active {
  transform: translateY(0);
  background-color: #dee2e6;
}

/* ================================
   API参数提示样式
   ================================ */
.param-item {
  display: flex;
  align-items: center;
  padding: 0.25rem 0;
}

.param-name {
  background-color: #f8f9fa;
  color: #495057;
  padding: 0.2rem 0.4rem;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 0.85rem;
  font-weight: 500;
  border: 1px solid #dee2e6;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 120px;
  display: inline-block;
}

.param-name:hover {
  background-color: #e9ecef;
  border-color: #adb5bd;
  transform: translateY(-1px);
}

#postParamsHelp,
#editPostParamsHelp {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 1rem;
}

#postParamsHelp .alert,
#editPostParamsHelp .alert {
  margin-bottom: 0;
  border-radius: 6px;
}

/* 参数项点击效果 */
.param-name:active {
  transform: translateY(0);
  background-color: #dee2e6;
}
